#ifndef __FEEDFORWARD_APP_H__
#define __FEEDFORWARD_APP_H__

#include "MyDefine.h"

// 运动状态枚举
typedef enum {
    MOTION_STATE_STATIC = 0,      // 静止
    MOTION_STATE_STRAIGHT,        // 直线运动
    MOTION_STATE_TURNING,         // 转弯
    MOTION_STATE_ACCELERATING     // 加速/减速
} MotionState_t;

// IMU数据结构
typedef struct {
    float ax, ay, az;    // 加速度 (m/s²)
    float wx, wy, wz;    // 角速度 (rad/s)
    float roll, pitch, yaw; // 姿态角 (度)
    uint8_t valid;       // 数据有效标志
} IMU_Data_t;

// 前馈控制器结构体
typedef struct {
    // 补偿系数
    float K_pitch;          // 俯仰补偿系数 (典型值: 0.5~1.0)
    float K_yaw;            // 偏航补偿系数 (典型值: 0.8~1.5)
    float K_centrifugal;    // 离心力补偿系数 (典型值: 0.3~0.8)
    
    // 物理参数
    float gimbal_height;    // 云台距离转动中心高度 (m)
    float car_velocity;     // 小车速度 (m/s)
    
    // IMU偏置校准
    float bias_ax, bias_ay, bias_az;
    float bias_wx, bias_wy, bias_wz;
    
    // 滤波器状态
    float filtered_ax, filtered_ay, filtered_az;
    float filtered_wx, filtered_wy, filtered_wz;
    float filter_alpha;     // 滤波系数 (0.1~0.3)
    
    // 运动状态识别
    MotionState_t motion_state;
    float turn_threshold;   // 转弯检测阈值 (rad/s)
    float accel_threshold;  // 加速度检测阈值 (m/s²)
    
    // 补偿输出
    float pitch_compensation;
    float yaw_compensation;
    
    // 系统状态
    uint8_t initialized;
    uint8_t calibrated;
    uint32_t calibration_samples;
    
} FeedforwardController_t;

// 全局变量声明
extern FeedforwardController_t ff_controller;
extern IMU_Data_t imu_data;

// 函数声明
void feedforward_init(void);
void feedforward_calibrate(void);
int8_t feedforward_update_imu_data(void);
void feedforward_process(void);
void feedforward_get_compensation(float *pitch_comp, float *yaw_comp);
void feedforward_set_parameters(float k_pitch, float k_yaw, float k_centrifugal);
void feedforward_set_physical_params(float height, float velocity);
MotionState_t feedforward_get_motion_state(void);
void feedforward_task(void);

// 调试函数
void feedforward_print_status(void);
void feedforward_print_compensation(void);

#endif /* __FEEDFORWARD_APP_H__ */
