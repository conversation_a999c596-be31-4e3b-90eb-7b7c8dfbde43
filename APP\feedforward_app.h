#ifndef __FEEDFORWARD_APP_H__
#define __FEEDFORWARD_APP_H__

#include "MyDefine.h"

// 杩愬姩鐘舵€佹灇涓?
typedef enum {
    MOTION_STATE_STATIC = 0,      // 闈欐??
    MOTION_STATE_STRAIGHT,        // 鐩寸嚎杩愬姩
    MOTION_STATE_TURNING,         // 杞?寮?
    MOTION_STATE_ACCELERATING     // 鍔犻€?/鍑忛€?
} MotionState_t;

// IMU鏁版嵁缁撴瀯
typedef struct {
    float ax, ay, az;    // 鍔犻€熷害 (m/s虏)
    float wx, wy, wz;    // 瑙掗€熷害 (rad/s)
    float roll, pitch, yaw; // 濮挎€佽?? (搴?)
    uint8_t valid;       // 鏁版嵁鏈夋晥鏍囧織
} IMU_Data_t;

// 鍓嶉?堟帶鍒跺櫒缁撴瀯浣?
typedef struct {
    // 琛ュ伩绯绘暟
    float K_pitch;          // 淇?浠拌ˉ鍋跨郴鏁? (鍏稿瀷鍊?: 0.5~1.0)
    float K_yaw;            // 鍋忚埅琛ュ伩绯绘暟 (鍏稿瀷鍊?: 0.8~1.5)
    float K_centrifugal;    // 绂诲績鍔涜ˉ鍋跨郴鏁? (鍏稿瀷鍊?: 0.3~0.8)
    
    // 鐗╃悊鍙傛暟
    float gimbal_height;    // 浜戝彴璺濈?昏浆鍔ㄤ腑蹇冮珮搴? (m)
    float car_velocity;     // 灏忚溅閫熷害 (m/s)
    
    // IMU鍋忕疆鏍″噯
    float bias_ax, bias_ay, bias_az;
    float bias_wx, bias_wy, bias_wz;
    
    // 婊ゆ尝鍣ㄧ姸鎬?
    float filtered_ax, filtered_ay, filtered_az;
    float filtered_wx, filtered_wy, filtered_wz;
    float filter_alpha;     // 婊ゆ尝绯绘暟 (0.1~0.3)
    
    // 杩愬姩鐘舵€佽瘑鍒?
    MotionState_t motion_state;
    float turn_threshold;   // 杞?寮?妫€娴嬮槇鍊? (rad/s)
    float accel_threshold;  // 鍔犻€熷害妫€娴嬮槇鍊? (m/s虏)
    
    // 琛ュ伩杈撳嚭
    float pitch_compensation;
    float yaw_compensation;
    
    // 绯荤粺鐘舵€?
    uint8_t initialized;
    uint8_t calibrated;
    uint32_t calibration_samples;
    
} FeedforwardController_t;

// 鍏ㄥ眬鍙橀噺澹版槑
extern FeedforwardController_t ff_controller;
extern IMU_Data_t imu_data;

// 鍑芥暟澹版槑
void feedforward_init(void);
void feedforward_calibrate(void);
int8_t feedforward_update_imu_data(void);
void feedforward_process(void);
void feedforward_get_compensation(float *pitch_comp, float *yaw_comp);
void feedforward_set_parameters(float k_pitch, float k_yaw, float k_centrifugal);
void feedforward_set_physical_params(float height, float velocity);
MotionState_t feedforward_get_motion_state(void);
void feedforward_task(void);

// 璋冭瘯鍑芥暟
void feedforward_print_status(void);
void feedforward_print_compensation(void);

#endif /* __FEEDFORWARD_APP_H__ */
