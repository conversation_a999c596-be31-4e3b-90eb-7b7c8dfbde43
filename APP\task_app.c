#include "task_app.h"
#include "pid_app.h" // 添加PID控制头文件
#include "uart_app.h"

//// 全局变量：存储最新的激光点坐标，初始化为(0,0)
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};     // 红色激光坐标
LaserCoord_t latest_blue_laser_coord = {BLUE_LASER_ID, 0, 0, 0}; // 绿色激光坐标（现在存储蓝紫光坐标）
RectCoord_t in_RectData = {RECT_ID, 0, 0, 0}; // Maixcam解析的矩形坐标
LaserCirclePoints_t Circle_pidTargetPoints; // 存储解析出的PID目标-圆的各点坐标
extern uint8_t pid_mode;

/**
 * @brief 解析MaixCam数据
 * @param buffer 包含数据的字符串缓冲区
 * @return 解析结果：0=成功，负数=错误码
 * @note 支持协议格式：
 *       1. $R,x,y,校验和\n (红色激光)
 *       2. $G,x,y,校验和\n (绿色激光)
 *       3. $M,点数量,x1,y1,x2,y2,...,校验和\n (矩形坐标)
 */
int pi_parse_data(char *buffer)
{
    if (!buffer || buffer[0] != '$')
        return -1; // 帧头错误

    // 找到最后一个逗号的位置，之后是校验和
    char *last_comma = strrchr(buffer, ',');
    if (!last_comma)
        return -2; // 数据格式错误，找不到校验和分隔符

    // 计算校验和
    int calc_checksum = 0;
    char *p = buffer;
    while (p < last_comma)
    {
        calc_checksum += (unsigned char)*p;
        p++;
    }
    calc_checksum &= 0xFF; // 取低8位

    // 解析接收到的校验和（十六进制）
    int actual_checksum = 0;
    if (sscanf(last_comma + 1, "%X", &actual_checksum) != 1)
        return -3; // 校验和格式错误

    if (calc_checksum != actual_checksum)
        return -4; // 校验和错误

    // 解析数据类型
    char data_type;
    if (sscanf(buffer, "$%c,", &data_type) != 1)
        return -5; // 数据类型解析失败

    // 根据数据类型进行不同的解析
    if (data_type == 'R' || data_type == 'B')
    {
        // 激光坐标解析：$R,x,y,校验和 或 $G,x,y,校验和
        int x, y;
        int parsed = sscanf(buffer, "$%c,%d,%d", &data_type, &x, &y);

        if (parsed != 3)
            return -6; // 激光坐标解析失败

        if (data_type == 'R')
        {
            // 更新红色激光坐标
            latest_red_laser_coord.type = 'R';
            latest_red_laser_coord.x = x;
            latest_red_laser_coord.y = y;
            latest_red_laser_coord.isValid = 1;

						// 红色激光坐标为目标值 
						app_pid_set_target(x,y);
//            // 直接更新PID系统的当前位置
//            app_pid_update_position(x, y);

//            my_printf(&huart1, "RED: X=%d, Y=%d\r\n", x, y);
        }
        else if (data_type == 'B')
        {
            // 更新绿色激光坐标
            latest_blue_laser_coord.type = 'B';
            latest_blue_laser_coord.x = x;
            latest_blue_laser_coord.y = y;
            latest_blue_laser_coord.isValid = 1;
//						app_pid_set_target(x,y);					
            // 直接更新PID系统的当前位置
            app_pid_update_position(x, y);
//            my_printf(&huart1, "BLUE: X=%d, Y=%d\r\n", x, y);
        }
    }
    else if (data_type == 'M')
    {
        // 矩形坐标解析：$M,点数量,x1,y1,x2,y2,...,校验和
        RectCoord_t rect_coord;
        rect_coord.type = 'M';

        // 解析点数量
        int parsed = sscanf(buffer, "$M,%d,", &rect_coord.point_count);
        if (parsed != 1 || rect_coord.point_count > 8 || rect_coord.point_count < 1)
            return -7; // 点数量解析失败或超出范围

        // 解析各个点的坐标
        char *data_start = strchr(buffer + 3, ',') + 1; // 跳过 "$M,"
        for (int i = 0; i < rect_coord.point_count; i++)
        {
            if (sscanf(data_start, "%d,%d,", &rect_coord.points[i][0], &rect_coord.points[i][1]) != 2)
                return -8; // 坐标解析失败

            // 移动到下一对坐标
            data_start = strchr(data_start, ',') + 1; // 跳过x
            data_start = strchr(data_start, ',') + 1; // 跳过y
        }

        // 打印矩形坐标信息
        my_printf(&huart1, "RECT: 点数=%d", rect_coord.point_count);
        for (int i = 0; i < rect_coord.point_count; i++)
        {
            my_printf(&huart1, " P%d=(%d,%d)", i+1, rect_coord.points[i][0], rect_coord.points[i][1]);
        }
        my_printf(&huart1, "\r\n");
				// 顺时针排序解析的坐标点
				sort_rect_clockwise(&rect_coord, &in_RectData);
				
				// 计算并存储插点法计算的点,但是这个函数目前只能计算矩形的四个角点
				//calculate_and_store_perimeter_points(&in_RectData);
				generate_perimeter_points(&in_RectData,&g_pidTargetPoints);
    }
    else
    {
        return -9; // 未知的数据类型
    }

    return 0; // 成功
}

/**
 * @brief 将矩形坐标点按顺时针方向排序
 * @param input 输入的矩形坐标结构体
 * @param output 输出的按顺时针排序后的矩形坐标结构体
 * @return 0=成功，负数=错误码
 */
int sort_rect_clockwise(RectCoord_t *input, RectCoord_t *output)
{
    if (!input || !output || input->point_count < 3)
        return -1; // 参数错误或点数不足
    
    // 复制基本信息
    output->type = input->type;
    output->point_count = input->point_count;
    
    // 计算中心点
    int center_x = 0, center_y = 0;
    for (int i = 0; i < input->point_count; i++) {
        center_x += input->points[i][0];
        center_y += input->points[i][1];
    }
    center_x /= input->point_count;
    center_y /= input->point_count;
    
    // 计算每个点相对于中心点的角度
    typedef struct {
        int index;
        float angle;
    } PointAngle;
    
    PointAngle angles[8];
    for (int i = 0; i < input->point_count; i++) {
        int dx = input->points[i][0] - center_x;
        int dy = input->points[i][1] - center_y;
        angles[i].index = i;
        angles[i].angle = atan2f((float)dy, (float)dx);
    }
    
    // 冒泡排序，按角度从小到大排序（顺时针）
    for (int i = 0; i < input->point_count - 1; i++) {
        for (int j = 0; j < input->point_count - i - 1; j++) {
            if (angles[j].angle < angles[j + 1].angle) {
                PointAngle temp = angles[j];
                angles[j] = angles[j + 1];
                angles[j + 1] = temp;
            }
        }
    }
    
    // 按排序后的顺序复制点坐标
    for (int i = 0; i < input->point_count; i++) {
        int src_idx = angles[i].index;
        output->points[i][0] = input->points[src_idx][0];
        output->points[i][1] = input->points[src_idx][1];
    }
    
    return 0;
}

/**
 * @brief 在矩形边框上均匀插入点，利用边的中点提高精度
 * @param in_RectData 输入的矩形坐标结构体
 * @param g_pidTargetPoints 输出的PID目标点序列
 * @return 0=成功，负数=错误码
 */
int generate_perimeter_points(RectCoord_t *in_RectData, PidTargetPoints_t *g_pidTargetPoints)
{
    if (!in_RectData || !g_pidTargetPoints || in_RectData->type != RECT_ID)
        return -1; // 参数错误
    
    // 确保矩形有8个点（4个角点和4个边的中点）
    if (in_RectData->point_count != 8)
        return -2; // 点数不正确
    
    // 初始化目标点计数
    g_pidTargetPoints->count = 0;
    
    // 假设点的排列为：角点0, 中点1, 角点2, 中点3, 角点4, 中点5, 角点6, 中点7
    // 处理四条边
    for (int edge = 0; edge < 4; edge++) {
        // 计算当前边的起点、中点和终点索引
        int start_idx = edge * 2;                  // 起始角点
        int mid_idx = (edge * 2 + 1) % 8;          // 边的中点
        int end_idx = (edge * 2 + 2) % 8;          // 结束角点
        
        // 获取三个点的坐标
        int start_x = in_RectData->points[start_idx][0];
        int start_y = in_RectData->points[start_idx][1];
        int mid_x = in_RectData->points[mid_idx][0];
        int mid_y = in_RectData->points[mid_idx][1];
        int end_x = in_RectData->points[end_idx][0];
        int end_y = in_RectData->points[end_idx][1];
        
        // 在这条边上均匀插入点，使用二次贝塞尔曲线
        for (int i = 0; i < POINTS_PER_SIDE; i++) {
            // 计算插值参数 t (0到1)
            float t = (float)i / POINTS_PER_SIDE;
            
            // 二次贝塞尔曲线公式: B(t) = (1-t)2P? + 2(1-t)tP? + t2P?
            // 其中P?是起点，P?是控制点(中点)，P?是终点
            float t1 = 1.0f - t;
            float a = t1 * t1;        // (1-t)2
            float b = 2.0f * t1 * t;  // 2(1-t)t
            float c = t * t;          // t2
            
            // 计算贝塞尔曲线上的点坐标
            int x = (int)(a * start_x + b * mid_x + c * end_x);
            int y = (int)(a * start_y + b * mid_y + c * end_y);
            
            // 存储计算出的点坐标
            g_pidTargetPoints->points[g_pidTargetPoints->count][0] = x;
            g_pidTargetPoints->points[g_pidTargetPoints->count][1] = y;
            g_pidTargetPoints->count++;
            
            // 安全检查，确保不会越界
            if (g_pidTargetPoints->count >= TOTAL_PERIMETER_POINTS)
						{
								pid_mode = 1; // 矩形描边
								app_pid_set_target(g_pidTargetPoints->points[0][0],g_pidTargetPoints->points[0][1]);
                break;
						}
        }
    }
    
    return 0;
}

/**
 * @brief 以给定激光坐标为中心生成圆形周围的等分点
 * 
 * 该函数根据传入的激光坐标，以该坐标为圆心，生成半径为CIRCLE_RADIUS的圆周上
 * CIRCLE_POINTS个等分点的坐标。如果输入坐标无效，则返回空结果。
 *
 * @param latest_green_laser_coord 绿色激光坐标，作为圆形的中心点
 * @return LaserCirclePoints_t 包含圆周等分点坐标的结构体
 */
LaserCirclePoints_t generateCirclePoints(LaserCoord_t latest_blue_laser_coord) 
{
    LaserCirclePoints_t result;
    result.count = CIRCLE_POINTS;
    
    // 如果输入的坐标无效，返回空结果
    if (!latest_blue_laser_coord.isValid) 
		{
        result.count = 0;
        return result;
    }
    
    int centerX = latest_blue_laser_coord.x;
    int centerY = latest_blue_laser_coord.y;
    
    // 生成圆周上的20个等分点
    for (int i = 0; i < CIRCLE_POINTS; i++) 
		{
        // 计算弧度（0-2π）
        double angle = i * (2 * M_PI / CIRCLE_POINTS);
        
        // 计算圆上的点坐标
        result.points[i][0] = (int)(centerX + CIRCLE_RADIUS * cos(angle));
        result.points[i][1] = (int)(centerY + CIRCLE_RADIUS * sin(angle));
    }
    
    return result;
}



