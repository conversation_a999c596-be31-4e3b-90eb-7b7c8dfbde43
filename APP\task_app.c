#include "task_app.h"
#include "pid_app.h" // 添加PID控制头文件

//// 全局变量：存储最新的激光点坐标，初始化为(0,0)
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};     // 红色激光坐标
LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0}; // 绿色激光坐标

/**
 * @brief 解析摄像头数据
 * @param buffer 包含激光点坐标信息的字符串缓冲区
 * @return 解析结果：0=解析失败，1=解析成功
 * @note 支持MaixCam发送的数据格式：red:(x,y)\n 或 gre:(x,y)\n
 *       解析成功后会更新全局变量latest_red_laser_coord或latest_green_laser_coord
 */
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; 

    int parsed_x, parsed_y; // 用于存储解析出的X,Y坐标
    int parsed_count;

    // 检查是否为 "red:(x,y)" 格式
    if (strncmp(buffer, "$,R", 3) == 0)
    {
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) // 必须解析出X和Y两个值
            return -2;         // 解析失败

        // 解析成功，更新全局红色激光坐标
//        latest_red_laser_coord.x = parsed_x;
//        latest_red_laser_coord.y = parsed_y;
//        latest_red_laser_coord.isValid = 1; // 标记数据为有效

        // 直接更新PID系统的当前位置
//        app_pid_update_position(parsed_x, parsed_y);

        // 打印调试信息
        my_printf(&huart1, "Parsed RED: X=%d, Y=%d\r\n", latest_red_laser_coord.x, latest_red_laser_coord.y);
    }
    // 检查是否为 "gre:(x,y)" 格式
    else if (strncmp(buffer, "$,G", 3) == 0)
    {
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) // 必须解析出X和Y两个值
            return -2;         // 解析失败

        // 解析成功，更新全局绿色激光坐标
//        latest_green_laser_coord.x = parsed_x;
//        latest_green_laser_coord.y = parsed_y;
//        latest_green_laser_coord.isValid = 1; // 标记数据为有效

        // 直接更新PID系统的当前位置
//        app_pid_update_position(parsed_x, parsed_y);

        // 打印调试信息
        my_printf(&huart1, "Parsed GRE: X=%d, Y=%d\r\n", latest_green_laser_coord.x, latest_green_laser_coord.y);
    }
    else if(strncmp(buffer, "$,M", 3))
    {
        // 既不是 "red:" 也不是 "gre:" 格式，无法识别的数据格式
        return -3; // 格式不支持
    }

    return 0; // 成功
}
