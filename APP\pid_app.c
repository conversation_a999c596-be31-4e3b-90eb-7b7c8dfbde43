#include "pid_app.h"
#include "uart_app.h"

// PID控制器实例
PID_T pid_x; // X轴PID控制器
PID_T pid_y; // Y轴PID控制器

// PID目标坐标
int target_x = 320; // 默认屏幕中心
int target_y = 240; // 默认屏幕中心

int current_x = 320;  // 当前实际X坐标
int current_y = 240;  // 当前实际Y坐标

// PID控制状态标志
bool pid_running = false;
extern uint8_t pid_mode;

PidParams_t pid_params_x = {
    .kp = 0.06f,    // 增大Kp提高响应速度
    .ki = 0.000f, // 增加积分项减小稳态误差
    .kd = 2.65f, // 增加微分项提高稳定性
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -60.0f,
    .i_max = 60.0f,
    .deadzone = 1 // 减小死区大小，提高精度
};

PidParams_t pid_params_y = {
    .kp = 0.04f,    // 增大Kp提高响应速度
    .ki = 0.000f, // 增加积分项减小稳态误差
    .kd = 1.65f, // 增加微分项提高稳定性
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -60.0f,
    .i_max = 60.0f,
    .deadzone = 1 // 减小死区大小，提高精度
};

//};

////积分限幅函数
//static void app_pid_limit_integral(PID_T *pid, float min, float max)
//{
//    if (pid->integral > max)
//    {
//        pid->integral = max;
//    }
//    else if (pid->integral < min)
//    {
//        pid->integral = min;
//    }
//}

//坐标PID控制器初始化
void app_pid_init(void)
{
    // 初始化X轴PID控制器
    pid_init(&pid_x,
             pid_params_x.kp, pid_params_x.ki, pid_params_x.kd,
             (float)current_x, pid_params_x.out_max);

    // 初始化Y轴PID控制器
    pid_init(&pid_y,
             pid_params_y.kp, pid_params_y.ki, pid_params_y.kd,
             (float)current_y, pid_params_y.out_max);

  //启动PID控制
  //app_pid_start();
	
	//启用PID信息上报
	//app_pid_report_task();
}

/**
 * @brief 设置PID控制器目标坐标
 * @param x X轴目标坐标
 * @param y Y轴目标坐标
 */
void app_pid_set_target(int x, int y)
{
    target_x = x;
    target_y = y;

    // 使用我们的PID中间件设置目标值
    pid_set_target(&pid_x, (float)target_x);
    pid_set_target(&pid_y, (float)target_y);
	
//	my_printf(&huart1, "X=%d, Y=%d\n", x, y);
}

/**
 * @brief 更新当前实际坐标
 * @param x X轴当前坐标
 * @param y Y轴当前坐标
 */
void app_pid_update_position(int x, int y)
{
    // 检查数据有效性
    if (x < 0 || x > 1920 || y < 0 || y > 1080)
    {
        return; // 无效数据直接丢弃
    }
		
		pid_x.current = x;
		pid_y.current = y;

    current_x = x;
    current_y = y;
}

extern LaserCoord_t latest_red_laser_coord; // 红色激光当前坐标
extern LaserCoord_t latest_blue_laser_coord; // 蓝紫色激光当前坐标
extern RectCoord_t outerRectData; // 外部矩形的坐标数据
extern PidTargetPoints_t g_pidTargetPoints; // 用于存储计算出的PID目标点——矩形
extern LaserCirclePoints_t Circle_pidTargetPoints; // 存储解析出的圆的坐标
uint16_t num = 0; // 当前跟随的坐标序列
/**
 * @brief 计算PID控制输出
 * 通过PID算法计算电机速度控制值
 */
void app_pid_calc(void)
{
    float output_x, output_y;

		if(pid_mode == 0)
			// 跟随系统
			pid_follow();
		else if(pid_mode == 1)
			// 矩形描边系统
			pid_ju();
		else if(pid_mode == 2)
			// 圆形描边系统
			pid_circle();
		else if(pid_mode == 3)	
			// 纯目标导向
			pid_only_target();

		if(!pid_running) return;

    // 只有同时在死区内才停止电机
    if (fabs(pid_x.error) <= pid_params_x.deadzone && fabs(pid_y.error) <= pid_params_y.deadzone)
    {
        my_printf(&huart1, "stop step_motor\n");
        Step_Motor_Stop();
        return;
    }
    // 使用位置式PID计算
    output_x = pid_calculate_positional(&pid_x, (float)pid_x.current);
    output_y = pid_calculate_positional(&pid_y, (float)pid_y.current);
    // 执行积分限幅
    pid_app_limit_integral(&pid_x, pid_params_x.i_min, pid_params_x.i_max);
    pid_app_limit_integral(&pid_y, pid_params_y.i_min, pid_params_y.i_max);
    // 限幅处理
    output_x = pid_constrain(output_x, pid_params_x.out_min, pid_params_x.out_max);
    output_y = pid_constrain(output_y, pid_params_y.out_min, pid_params_y.out_max);

    // 获取前馈补偿
    float ff_pitch_comp = 0.0f, ff_yaw_comp = 0.0f;
    feedforward_get_compensation(&ff_pitch_comp, &ff_yaw_comp);

    // 将前馈补偿叠加到PID输出
    // 注意：这里假设X轴对应偏航(Yaw)，Y轴对应俯仰(Pitch)
    // 根据实际云台安装方向可能需要调整
    float final_output_x = output_x + ff_yaw_comp;    // X轴 + 偏航补偿
    float final_output_y = output_y + ff_pitch_comp;  // Y轴 + 俯仰补偿

    // 最终输出限幅
    final_output_x = pid_constrain(final_output_x, pid_params_x.out_min, pid_params_x.out_max);
    final_output_y = pid_constrain(final_output_y, pid_params_y.out_min, pid_params_y.out_max);

		my_printf(&huart5,"PID: %.2f,%.2f FF: %.2f,%.2f Final: %.2f,%.2f\r\n",
                     output_x, output_y, ff_yaw_comp, ff_pitch_comp, final_output_x, final_output_y);

    // 控制电机,正负根据实际可调——电赛使用
    Step_Motor_Set_Speed_my(-final_output_x, -final_output_y);
		
//		Step_Motor_Set_Dis((int32_t)-output_x,(int32_t)-output_y);
}

//PID控制任务函数
void app_pid_task(void)
{
    if (pid_running)
    {
        // 执行PID计算
        app_pid_calc();
		}	
}

//启动PID控制
void app_pid_start(void)
{
    // 如果PID控制已经在运行，直接返回
    if (pid_running)
    {
        return;
    }
		
    my_printf(&huart1, "启动PID控制\n");

    // 设置激光坐标回调函数
    //maixcam_set_callback(pid_laser_coord_callback);

    // 重置PID控制器
    pid_reset(&pid_x);//清除所有历史误差数据
    pid_reset(&pid_y);

    // 设置PID运行标志
    pid_running = true;
}

//停止PID控制
void app_pid_stop(void)
{
    // 如果PID控制已停止，直接返回
    if (!pid_running)
    {
        return;
    }
    my_printf(&PID_REPORT_UART, "停止PID控制\n");
		
		// 清除PID运行标志
    pid_running = false;

    // 停止电机
//    Step_Motor_Stop();
}

/**
 * @brief 设置X轴PID参数
 * @param params 新的PID参数结构体
 */
void app_pid_set_x_params(PidParams_t params)
{
    // 更新参数结构体
    pid_params_x = params;

    // 更新PID控制器参数
    pid_set_params(&pid_x, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_x, params.out_max);
}

/**
 * @brief 设置Y轴PID参数
 * @param params 新的PID参数结构体
 */
void app_pid_set_y_params(PidParams_t params)
{
    // 更新参数结构体
    pid_params_y = params;

    // 更新PID控制器参数
    pid_set_params(&pid_y, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_y, params.out_max);
}

/**
 * @brief 解析上位机目标值
 * @param scale_value 放大后的值
 * @return 实际值
 */
int app_pid_parse_target(int scale_value)
{
    // 根据上位机数据格式，转换为实际坐标值
    return scale_value / PID_REPORT_SCALE;
}

/**
 * @brief 解析上位机指令
 * @param cmd 上位机指令字符串
 */
void app_pid_parse_cmd(char *cmd)
{
    int val1, val2, val3;

	// 解析上位机设置的目标位置   T:100,200 - - > T:1,2
    if (sscanf(cmd, "T:%d,%d", &val1, &val2) == 2)
    {

        target_x = app_pid_parse_target(val1);
        target_x = app_pid_parse_target(val2);

			  app_pid_set_target(target_x,target_x);

        my_printf(&PID_REPORT_UART, "收到目标位置命令: X=%d, Y=%d\n", target_x, target_y);
    }
    // 解析X轴PID参数设置 - PX:kp,ki,kd----PX:300,60,15
    else if (sscanf(cmd, "PX:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_x = pid_params_x;
        new_params_x.kp = val1 / 100.0f;
        new_params_x.ki = val2 / 100.0f;
        new_params_x.kd = val3 / 100.0f;
        app_pid_set_x_params(new_params_x);

      my_printf(&PID_REPORT_UART, "更新X轴PID参数: kp=%.2f, ki=%.2f, kd=%.2f\n",
                 new_params_x.kp, new_params_x.ki, new_params_x.kd);
    }
    // 解析Y轴PID参数设置 - PY:kp,ki,kd-----PY:280,55,12
    else if (sscanf(cmd, "PY:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_y = pid_params_y;
        new_params_y.kp = val1 / 100.0f;
        new_params_y.ki = val2 / 100.0f;
        new_params_y.kd = val3 / 100.0f;
        app_pid_set_y_params(new_params_y);

        my_printf(&PID_REPORT_UART, "更新Y轴PID参数: kp=%.2f, ki=%.2f, kd=%.2f\n",
                  new_params_y.kp, new_params_y.ki, new_params_y.kd);
    }
    // 解析启动/停止命令
    else if (strncmp(cmd, "START", 5) == 0)
    {
        app_pid_start();
    }
    else if (strncmp(cmd, "STOP", 4) == 0)
    {
				pid_mode = 0;
        app_pid_stop();
    }
    else if (sscanf(cmd, "R: X=%d, Y=%d", &val1, &val2) == 2)//R: X=50, Y=150
    {
				app_pid_set_target(val1, val2);

        my_printf(&PID_REPORT_UART, "直接设置目标位置: X=%d, Y=%d\n", target_x, target_y);
        // 第一次读取到坐标值
        app_pid_init();
        // 设置默认目标位置

        // 如果PID未启动，自动启动
        if (!pid_running)
        {
            app_pid_start();
        }
    }
    // 前馈控制命令
    else if (strncmp(cmd, "FF_CAL", 6) == 0)
    {
        ff_controller.calibrated = 0;
        ff_controller.calibration_samples = 0;
        my_printf(&PID_REPORT_UART, "开始前馈校准，请保持设备静止...\n");
    }
    else if (sscanf(cmd, "FF:%d,%d,%d", &val1, &val2, &val3) == 3) // FF:80,120,50
    {
        float k_pitch = val1 / 100.0f;
        float k_yaw = val2 / 100.0f;
        float k_centrifugal = val3 / 100.0f;
        feedforward_set_parameters(k_pitch, k_yaw, k_centrifugal);
        my_printf(&PID_REPORT_UART, "前馈参数: K_pitch=%.2f, K_yaw=%.2f, K_centrifugal=%.2f\n",
                 k_pitch, k_yaw, k_centrifugal);
    }
    else if (strncmp(cmd, "FF_STATUS", 9) == 0)
    {
        feedforward_print_status();
        feedforward_print_compensation();
    }
}

void pid_circle(void)
{
		/* 圆形描边 */
		pid_x.error = pid_x.target - pid_x.current;
    pid_y.error = pid_y.target - pid_y.current;
		my_printf(&huart1,"%d,%.2f,%.2f\r\n",num,pid_x.error,pid_y.error);
		if(num == CIRCLE_POINTS)
		{
			app_pid_set_target(Circle_pidTargetPoints.points[0][0], Circle_pidTargetPoints.points[0][1]);
		}
		if(num > CIRCLE_POINTS)
		{
			num = 0;
			memset(&Circle_pidTargetPoints,0,sizeof(Circle_pidTargetPoints));
			Step_Motor_Stop();
			app_pid_stop();
			return;
		}
	
		if( fabs(pid_x.error) <= 3 && fabs(pid_y.error) <= 3)
		{
			num++;
			app_pid_set_target(Circle_pidTargetPoints.points[num][0], Circle_pidTargetPoints.points[num][1]);
		}
}

void pid_ju(void)
{
		/* 矩形描边系统 */
		pid_x.error = pid_x.target - pid_x.current;
    pid_y.error = pid_y.target - pid_y.current;
	
//		my_printf(&huart1,"%d,%.2f,%.2f\r\n",num,pid_x.target,pid_x.current);
	
		my_printf(&huart1,"%d,%.2f,%.2f\r\n",num,pid_x.error,pid_y.error);
//		my_printf(&huart1,"num: %d\r\n",num);
	
		if(num == TOTAL_PERIMETER_POINTS)
		{
			app_pid_set_target(g_pidTargetPoints.points[0][0], g_pidTargetPoints.points[0][1]);
		}
		if(num > TOTAL_PERIMETER_POINTS)
		{
			num = 0;
			memset(&g_pidTargetPoints,0,sizeof(g_pidTargetPoints));
			Step_Motor_Stop();
			app_pid_stop();
			return;
		}
	
		if( fabs(pid_x.error) <= 3 && fabs(pid_y.error) <= 3)
		{
			num++;
			app_pid_set_target(g_pidTargetPoints.points[num][0], g_pidTargetPoints.points[num][1]);
		}
}

void pid_follow(void)
{
		/* 跟踪系统 */
    // 计算X轴误差
    pid_x.error = latest_blue_laser_coord.x - latest_red_laser_coord.x;
    pid_y.error = latest_blue_laser_coord.y - latest_red_laser_coord.y;
//		my_printf(&huart1,"%.2f,%.2f\r\n",pid_x.target,pid_x.current);
		my_printf(&huart1,"%.2f,%.2f\r\n",pid_x.error,pid_y.error);
}

void pid_only_target(void)
{
		pid_x.error = pid_x.target - pid_x.current;
    pid_y.error = pid_y.target - pid_y.current;
	
		if(fabs(pid_x.error) < 10 && fabs(pid_y.error) < 8)
			HAL_GPIO_WritePin(Relay_GPIO_Port, Relay_Pin, GPIO_PIN_SET);
//		else
//			HAL_GPIO_WritePin(Relay_GPIO_Port, Relay_Pin, GPIO_PIN_RESET);
	
		my_printf(&huart1,"%.2f,%.2f\r\n",pid_x.error,pid_y.error);
}


