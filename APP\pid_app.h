#ifndef __PID_APP_H__
#define __PID_APP_H__

#include "MyDefine.h"

// 上位机接口相关定义
#define PID_REPORT_SCALE 100   // 上报数据放大系数
#define PID_REPORT_UART huart1 // 上报使用的串口


// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
    float i_min;       // 积分项最小值
    float i_max;       // 积分项最大值
    float deadzone;    // 死区大小
} PidParams_t;

void app_pid_init(void);
void app_pid_set_target(int x, int y);
void app_pid_update_position(int x, int y);
void app_pid_start(void);
void app_pid_stop(void);
void app_pid_calc(void);
void app_pid_task(void);
void app_pid_parse_cmd(char *cmd);

//static void app_pid_limit_integral(PID_T *pid, float min, float max);


#endif
