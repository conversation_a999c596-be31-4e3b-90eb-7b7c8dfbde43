from maix import image, display, app, time, camera, uart  # MaixCAM硬件接口库
import cv2  # OpenCV图像处理库
import numpy as np  # 数值计算库

disp = display.Display()  # 初始化显示器对象
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)  # 初始化摄像头，分辨率320x240，BGR格式

# 初始化串口，使用设备路径/dev/ttyS0（UART0）波特率115200
serial = uart.UART("/dev/ttyS0", 115200)

# 定义通信协议相关常量
FRAME_HEADER = '$'  # 数据帧头标识符
FRAME_FOOTER = '\n'  # 数据帧尾标识符
RED_LASER_ID = 'R'  # 红色激光标识符
GREEN_LASER_ID = 'G'  # 绿色激光标识符
RECT_ID = 'M'  # M表示矩形(Middle Rectangle)坐标

# 定义矩形过滤参数
min_area = 5000  # 最小矩形面积阈值
max_area = 40000  # 最大矩形面积阈值
min_aspect_ratio = 0.2  # 最小长宽比阈值
max_aspect_ratio = 5  # 最大长宽比阈值
corner_threshold = 8  # 角点位置接近的距离阈值

# 新增变量用于记录内外框检测结果
rect_detection_results = []  # 存储矩形检测历史结果
DETECTION_TIMES = 10  # 稳定性检测需要的连续检测次数
POSITION_CHANGE_THRESHOLD = 10  # 位置变化阈值，用于判断矩形位置是否稳定
middle_rect_points = []  # 存储中间矩形的关键点坐标
SERIAL_POLL_INTERVAL_MS = 5  # 串口发送轮询间隔，单位毫秒
last_send_time = 0  # 记录上次发送数据的时间戳
send_red_next = True  # 交替发送标志，True表示下次发送红色激光坐标
rect_sent = False  # 矩形坐标发送状态标志，防止重复发送

# 发送激光坐标数据函数
def send_laser_coordinates(laser_type, coords):
    """发送激光点坐标到串口，数据格式：$类型,X,Y,校验和\n"""
    if coords is None:  # 检查坐标是否有效
        return

    # 构建数据帧：$类型,X坐标,Y坐标,校验和\n
    # 类型：R表示红色激光，G表示绿色激光
    x, y = coords  # 解包坐标
    data = f"{FRAME_HEADER}{laser_type},{x},{y}"  # 构建基础数据帧

    # 计算简单校验和（所有字节ASCII码相加的低8位）
    checksum = 0
    for c in data:  # 遍历数据帧中的每个字符
        checksum += ord(c)  # 累加ASCII码值
    checksum &= 0xFF  # 取低8位作为校验和

    # 添加校验和和帧尾
    data += f",{checksum:02X}{FRAME_FOOTER}"  # 校验和转为2位十六进制

    # 通过串口发送数据
    serial.write(data.encode())  # 编码为字节后发送
    print(f"串口发送: {data}")  # 打印发送的数据用于调试

# 发送矩形坐标数据函数
def send_rectangle_coordinates(rect_points):
    """发送矩形关键点坐标到串口，数据格式：$M,点数量,x1,y1,x2,y2,...,校验和\n"""
    if rect_points is None or len(rect_points) == 0:  # 检查点集是否有效
        return

    # 构建数据帧：$M,点数量,x1,y1,x2,y2,...,校验和\n
    # M表示矩形坐标，点数量表示坐标点的个数
    point_count = len(rect_points)  # 获取点的数量
    coords_str = ",".join([f"{x},{y}" for x, y in rect_points])  # 将所有坐标点转为字符串
    data = f"{FRAME_HEADER}{RECT_ID},{point_count},{coords_str}"  # 构建基础数据帧

    # 计算简单校验和（所有字节ASCII码相加的低8位）
    checksum = 0
    for c in data:  # 遍历数据帧中的每个字符
        checksum += ord(c)  # 累加ASCII码值
    checksum &= 0xFF  # 取低8位作为校验和

    # 添加校验和和帧尾
    data += f",{checksum:02X}{FRAME_FOOTER}"  # 校验和转为2位十六进制

    # 通过串口发送数据
    serial.write(data.encode())  # 编码为字节后发送
    print(f"串口发送: {data}")  # 打印发送的数据用于调试

def detect_rectangles(img):
    """检测图像中的矩形轮廓，返回最大的两个矩形"""
    # 转换为灰度图像
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)  # BGR转灰度

    # 高斯平滑滤波
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)  # 5x5高斯核平滑，减少噪声

    # 调整边缘检测参数
    edged = cv2.Canny(blurred, 50, 150)  # Canny边缘检测，低阈值50，高阈值150

    # 定义膨胀核
    kernel = np.ones((3, 3), np.uint8)  # 3x3膨胀核
    # 对边缘图像进行膨胀操作
    dilated_edges = cv2.dilate(edged, kernel, iterations=1)  # 膨胀1次，连接断开的边缘

    # 查找轮廓，使用 RETR_TREE 模式来检测内外轮廓
    contours, _ = cv2.findContours(dilated_edges.copy(), cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)  # 忽略hierarchy

    # 矩形检测
    rectangles = []  # 存储检测到的矩形
    for i, contour in enumerate(contours):  # 遍历所有轮廓
        perimeter = cv2.arcLength(contour, True)  # 计算轮廓周长
        approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)  # 多边形逼近，精度为周长的4%
        if len(approx) == 4:  # 如果逼近结果是四边形
            # 计算矩形的面积
            area = cv2.contourArea(approx)  # 计算轮廓面积
            # 计算矩形的边界框
            _, _, w, h = cv2.boundingRect(approx)  # 获取边界框宽高，忽略x,y坐标
            # 计算长宽比
            aspect_ratio = float(w) / h if h != 0 else 0  # 防止除零错误
            # 过滤不符合条件的矩形
            if min_area < area < max_area and min_aspect_ratio < aspect_ratio < max_aspect_ratio:
                rectangles.append(approx)  # 添加符合条件的矩形

    # 根据角点位置合并接近的矩形
    merged_rectangles = []  # 存储合并后的矩形
    for rect in rectangles:  # 遍历所有检测到的矩形
        found_match = False  # 是否找到匹配的矩形
        for i, merged_rect in enumerate(merged_rectangles):  # 遍历已合并的矩形
            corner_matches = 0  # 匹配的角点数量
            for corner1 in rect:  # 遍历当前矩形的角点
                for corner2 in merged_rect:  # 遍历已合并矩形的角点
                    distance = np.linalg.norm(np.array(corner1[0]) - np.array(corner2[0]))  # 计算角点距离
                    if distance < corner_threshold:  # 如果距离小于阈值
                        corner_matches += 1  # 匹配角点数+1
                        break
            if corner_matches >= 3:  # 如果有3个或以上角点匹配
                found_match = True  # 找到匹配
                if cv2.contourArea(rect) > cv2.contourArea(merged_rect):  # 如果当前矩形面积更大
                    merged_rectangles[i] = rect  # 替换为面积更大的矩形
                break
        if not found_match:  # 如果没有找到匹配
            merged_rectangles.append(rect)  # 添加为新的矩形

    # 对合并后的矩形按面积排序
    merged_rectangles.sort(key=lambda r: cv2.contourArea(r), reverse=True)  # 按面积从大到小排序

    # 进一步过滤，只保留最大的两个矩形
    if len(merged_rectangles) > 2:  # 如果矩形数量超过2个
        merged_rectangles = merged_rectangles[:2]  # 只保留前两个最大的

    return merged_rectangles  # 返回检测到的矩形列表

def is_position_stable(results):
    """判断矩形位置是否稳定，通过分析连续检测结果的标准差"""
    if len(results) < DETECTION_TIMES:  # 如果检测次数不足
        return False  # 返回不稳定
    outer_rects = [result[0] for result in results]  # 提取外框矩形
    inner_rects = [result[1] for result in results]  # 提取内框矩形

    outer_corners = [rect.flatten() for rect in outer_rects]  # 将外框角点展平为一维数组
    inner_corners = [rect.flatten() for rect in inner_rects]  # 将内框角点展平为一维数组

    outer_std = np.std(outer_corners, axis=0)  # 计算外框角点的标准差
    inner_std = np.std(inner_corners, axis=0)  # 计算内框角点的标准差

    # 如果所有角点的标准差都小于阈值，则认为位置稳定
    return np.all(outer_std < POSITION_CHANGE_THRESHOLD) and np.all(inner_std < POSITION_CHANGE_THRESHOLD)

def draw_middle_rect(outer_rect, inner_rect, img):
    """在外框和内框之间绘制中间矩形，并计算关键点"""
    outer_points = outer_rect.reshape(-1, 2)  # 重塑外框点为Nx2数组
    inner_points = inner_rect.reshape(-1, 2)  # 重塑内框点为Nx2数组

    middle_points = []  # 存储中间矩形的角点
    for i in range(4):  # 计算4个角点的中点
        mid_point = ((outer_points[i][0] + inner_points[i][0]) // 2, (outer_points[i][1] + inner_points[i][1]) // 2)
        middle_points.append(mid_point)  # 添加角点中点

    # 计算边的中点
    for i in range(4):  # 计算4条边的中点
        next_i = (i + 1) % 4  # 下一个角点索引（循环）
        mid_edge_point = ((middle_points[i][0] + middle_points[next_i][0]) // 2, (middle_points[i][1] + middle_points[next_i][1]) // 2)
        middle_points.append(mid_edge_point)  # 添加边中点

    global middle_rect_points  # 声明全局变量
    middle_rect_points = middle_points  # 保存中间矩形的所有关键点

    # 绘制中间矩形的边框
    for i in range(4):  # 绘制4条边
        next_i = (i + 1) % 4  # 下一个角点索引
        cv2.line(img, middle_points[i], middle_points[next_i], (255, 0, 0), 2)  # 蓝色线条，线宽2

    # 绘制所有关键点
    for point in middle_points:  # 遍历所有关键点
        cv2.circle(img, point, 5, (255, 0, 0), -1)  # 蓝色实心圆，半径5

    return img  # 返回绘制后的图像

def detect_lasers(img):
    """检测图像中的红色和绿色激光点，返回最亮激光点的坐标"""
    # 初始化最亮激光点信息
    brightest_red_coords = None  # 最亮红色激光点坐标
    brightest_green_coords = None  # 最亮绿色激光点坐标
    max_brightness_red = 0  # 红色激光最大亮度值
    max_brightness_green = 0  # 绿色激光最大亮度值

    # 转换颜色空间为 HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)  # BGR转HSV，便于颜色分割

    # 定义激光点颜色范围（HSV色彩空间）
    lower_red1 = np.array([0, 100, 50])    # 红色范围1下界（0-10度）
    upper_red1 = np.array([10, 255, 255])  # 红色范围1上界
    lower_red2 = np.array([160, 100, 50])  # 红色范围2下界（160-180度）
    upper_red2 = np.array([180, 255, 255]) # 红色范围2上界
    lower_green = np.array([40, 100, 50])  # 绿色范围下界（40-80度）
    upper_green = np.array([80, 255, 255]) # 绿色范围上界

    # 创建红色和绿色激光的二值化图像
    mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)  # 红色范围1掩码
    mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)  # 红色范围2掩码
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)       # 合并两个红色掩码
    mask_green = cv2.inRange(hsv, lower_green, upper_green) # 绿色掩码

    # 闭运算（先膨胀后腐蚀），填补激光点内部的小孔
    kernel = np.ones((5, 5), np.uint8)  # 5x5形态学核
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)    # 红色掩码闭运算
    mask_green = cv2.morphologyEx(mask_green, cv2.MORPH_CLOSE, kernel) # 绿色掩码闭运算

    # 寻找红色激光外轮廓
    contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours_red:  # 遍历所有红色轮廓
        rect = cv2.minAreaRect(contour)  # 获取最小外接矩形
        laser_coords = tuple(map(int, rect[0]))  # 矩形中心坐标转为整数
        r_sum, g_sum = get_pixel_sum(img, laser_coords)  # 获取该点周围的R、G通道亮度和
        # 仅当红色分量大于绿色分量且亮度高于当前最大值时更新
        if r_sum > g_sum and r_sum > max_brightness_red:
            max_brightness_red = r_sum  # 更新最大红色亮度
            brightest_red_coords = laser_coords  # 更新最亮红色激光坐标

    # 寻找绿色激光外轮廓
    contours_green, _ = cv2.findContours(mask_green, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours_green:  # 遍历所有绿色轮廓
        rect = cv2.minAreaRect(contour)  # 获取最小外接矩形
        laser_coords = tuple(map(int, rect[0]))  # 矩形中心坐标转为整数
        r_sum, g_sum = get_pixel_sum(img, laser_coords)  # 获取该点周围的R、G通道亮度和
        # 仅当绿色分量大于红色分量且亮度高于当前最大值时更新
        if g_sum > r_sum and g_sum > max_brightness_green:
            max_brightness_green = g_sum  # 更新最大绿色亮度
            brightest_green_coords = laser_coords  # 更新最亮绿色激光坐标

    # 绘制最亮的红色激光点
    if brightest_red_coords:  # 如果检测到红色激光
        cv2.circle(img, brightest_red_coords, 5, (0, 0, 255), -1)  # 绘制红色实心圆
        cv2.putText(img, "Red Laser", (brightest_red_coords[0], brightest_red_coords[1] - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)  # 添加红色文字标签

    # 绘制最亮的绿色激光点
    if brightest_green_coords:  # 如果检测到绿色激光
        cv2.circle(img, brightest_green_coords, 5, (0, 255, 0), -1)  # 绘制绿色实心圆
        cv2.putText(img, "Green Laser", (brightest_green_coords[0], brightest_green_coords[1] - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)  # 添加绿色文字标签

    return brightest_red_coords, brightest_green_coords  # 返回两个激光点坐标

def get_pixel_sum(image, coords):
    """获取指定坐标周围区域的R、G通道像素值总和"""
    # 获取图像宽度和高度
    height, width = image.shape[:2]  # 获取图像尺寸
    radius = 3  # 采样区域半径
    # 确定采样区域的边界坐标
    x, y = coords  # 解包坐标
    x_start = max(0, x - radius)        # 左边界，防止越界
    y_start = max(0, y - radius)        # 上边界，防止越界
    x_end = min(width - 1, x + radius)  # 右边界，防止越界
    y_end = min(height - 1, y + radius) # 下边界，防止越界

    # 提取感兴趣区域（ROI）
    roi = image[y_start:y_end, x_start:x_end]  # 裁剪出采样区域

    # 计算 R 和 G 通道总值（BGR格式中B=0,G=1,R=2）
    # 选取红色通道
    r_channel = roi[:, :, 2]  # 红色通道（BGR中的R）
    # 绿色通道
    g_channel = roi[:, :, 1]  # 绿色通道（BGR中的G）
    # 求和
    r_sum = int(r_channel.sum())  # 红色通道像素值总和
    g_sum = int(g_channel.sum())  # 绿色通道像素值总和

    return r_sum, g_sum  # 返回R、G通道亮度总和

rect_detection_finished = False  # 矩形检测完成标志
while not app.need_exit():  # 主循环，直到程序退出
    img = cam.read()  # 从摄像头读取一帧图像
    current_time_ms = time.ticks_ms()  # 获取当前时间戳（毫秒）
    # convert maix.image.Image object to numpy.ndarray object
    t = time.ticks_ms()  # 记录转换开始时间
    img = image.image2cv(img, ensure_bgr=False, copy=False)  # 将MaixCAM图像转为OpenCV格式
    print("time: ", time.ticks_ms() - t)  # 打印图像转换耗时

    if not rect_detection_finished:  # 如果矩形检测尚未完成
        merged_rectangles = detect_rectangles(img)  # 检测图像中的矩形

        if len(merged_rectangles) == 2:  # 如果检测到2个矩形（内外框）
            rect_detection_results.append(merged_rectangles)  # 将检测结果添加到历史记录

            if is_position_stable(rect_detection_results):  # 如果矩形位置稳定
                outer_rect = merged_rectangles[0]  # 外框（面积较大）
                inner_rect = merged_rectangles[1]  # 内框（面积较小）
                img = draw_middle_rect(outer_rect, inner_rect, img)  # 绘制中间矩形
                print("Middle rectangle points:", middle_rect_points)  # 打印中间矩形关键点
                rect_detection_finished = True  # 标记矩形检测完成
                # 发送矩形坐标数据（仅发送一次）
                if not rect_sent and middle_rect_points:  # 如果尚未发送且有关键点数据
                    send_rectangle_coordinates(middle_rect_points)  # 发送矩形坐标
                    rect_sent = True  # 标记已发送
            else:  # 如果位置不稳定，继续检测
                outer_rect = merged_rectangles[0]  # 外框
                inner_rect = merged_rectangles[1]  # 内框
                outer_color = (0, 0, 255)  # 外框显示为红色
                inner_color = (0, 255, 0)  # 内框显示为绿色
                cv2.drawContours(img, [outer_rect], -1, outer_color, 2)  # 绘制外框轮廓
                cv2.putText(img, "Outer Frame", (outer_rect[0][0][0], outer_rect[0][0][1] - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, outer_color, 2)  # 外框标签
                cv2.drawContours(img, [inner_rect], -1, inner_color, 2)  # 绘制内框轮廓
                cv2.putText(img, "Inner Frame", (inner_rect[0][0][0], inner_rect[0][0][1] - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, inner_color, 2)  # 内框标签
        elif len(merged_rectangles) == 1:  # 如果只检测到1个矩形
            rect = merged_rectangles[0]  # 获取检测到的矩形
            if cv2.contourArea(rect) > (max_area + min_area) / 2:  # 根据面积判断是内框还是外框
                color = (0, 0, 255)  # 大面积为外框，红色显示
                label = "Outer Frame"
            else:
                color = (0, 255, 0)  # 小面积为内框，绿色显示
                label = "Inner Frame"
            cv2.drawContours(img, [rect], -1, color, 2)  # 绘制矩形轮廓
            cv2.putText(img, label, (rect[0][0][0], rect[0][0][1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)  # 添加标签
    else:  # 如果矩形检测已完成
        # 绘制中间矩形的关键点
        for point in middle_rect_points:  # 遍历所有关键点
            cv2.circle(img, point, 5, (255, 0, 0), -1)  # 绘制蓝色实心圆点

    # 检测激光点
    red_laser_coords, green_laser_coords = detect_lasers(img)  # 检测红色和绿色激光点

    # 检查是否达到串口发送间隔时间
    if current_time_ms - last_send_time >= SERIAL_POLL_INTERVAL_MS:  # 时间间隔控制
        # 交替发送红色和绿色激光坐标，避免串口拥塞
        if send_red_next:  # 如果轮到发送红色激光
            if red_laser_coords:  # 如果检测到红色激光
                print("Red laser coordinates:", red_laser_coords)  # 打印红色激光坐标
                send_laser_coordinates(RED_LASER_ID, red_laser_coords)  # 发送红色激光坐标
                last_send_time = current_time_ms  # 更新发送时间
                send_red_next = False  # 下次发送绿色激光
            elif green_laser_coords:  # 如果没有红色激光，但有绿色激光，则发送绿色
                print("Green laser coordinates:", green_laser_coords)  # 打印绿色激光坐标
                send_laser_coordinates(GREEN_LASER_ID, green_laser_coords)  # 发送绿色激光坐标
                last_send_time = current_time_ms  # 更新发送时间
                send_red_next = True  # 保持下次发送红色激光的顺序
        else:  # 如果轮到发送绿色激光
            if green_laser_coords:  # 如果检测到绿色激光
                print("Green laser coordinates:", green_laser_coords)  # 打印绿色激光坐标
                send_laser_coordinates(GREEN_LASER_ID, green_laser_coords)  # 发送绿色激光坐标
                last_send_time = current_time_ms  # 更新发送时间
                send_red_next = True  # 下次发送红色激光
            elif red_laser_coords:  # 如果没有绿色激光，但有红色激光，则发送红色
                print("Red laser coordinates:", red_laser_coords)  # 打印红色激光坐标
                send_laser_coordinates(RED_LASER_ID, red_laser_coords)  # 发送红色激光坐标
                last_send_time = current_time_ms  # 更新发送时间
                send_red_next = False  # 保持下次发送绿色激光的顺序

    # 显示处理后的图像
    img_show = image.cv2image(img, bgr=True, copy=False)  # 将OpenCV图像转回MaixCAM格式
    disp.show(img_show)  # 在显示器上显示图像
