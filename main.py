from maix import image, display, app, time, camera
import cv2
import numpy as np
import math
from micu_uart_lib import (
    SimpleUART, micu_printf
)

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    """
    紫色激光点检测器

    基于HSV颜色空间进行紫色激光点的识别和定位，使用形态学操作优化检测效果。
    适用于室内环境下的激光点跟踪应用，能够准确识别紫色激光在复杂背景中的位置。

    算法原理：
    1. BGR→HSV颜色空间转换：HSV空间对光照变化更鲁棒，便于颜色分割
    2. 颜色阈值过滤：设定紫色HSV范围[130-160, 80-255, 80-255]进行二值化
    3. 形态学闭运算：使用3x3矩形核消除噪声点并填补激光点内部空洞
    4. 轮廓检测：提取连通区域并计算最小外接矩形获取激光点中心坐标

    Attributes:
        pixel_radius (int): 绘制激光点标记的像素半径，默认3像素
        kernel (np.ndarray): 形态学操作的结构元素，3x3矩形核用于闭运算

    Example:
        detector = PurpleLaserDetector(pixel_radius=5)
        img_with_markers, laser_points = detector.detect(input_image)
        print(f"检测到{len(laser_points)}个激光点")
    """

    def __init__(self, pixel_radius=3):
        """
        初始化紫色激光检测器

        Args:
            pixel_radius (int): 激光点标记的绘制半径，取值范围[1-10]，默认3
                              较大值便于可视化，较小值减少遮挡
        """
        self.pixel_radius = pixel_radius  # 激光点标记半径
        self.kernel = np.ones((3, 3), np.uint8)  # 3x3矩形结构元素，用于形态学闭运算

    def detect(self, img):
        """
        检测图像中的紫色激光点

        使用HSV颜色空间和形态学操作检测紫色激光点，并在原图上标记检测结果。

        算法步骤：
        1. BGR→HSV颜色空间转换，提高颜色识别的鲁棒性
        2. 基于HSV阈值进行颜色过滤，提取紫色区域
        3. 形态学闭运算去除噪声并填补空洞
        4. 轮廓检测获取激光点位置
        5. 计算轮廓的最小外接矩形中心作为激光点坐标

        Args:
            img (np.ndarray): 输入的BGR格式图像，shape为(H, W, 3)

        Returns:
            tuple: (标记后的图像, 激光点坐标列表)
                - img (np.ndarray): 在原图上绘制了激光点标记的图像
                - laser_points (list): 激光点坐标列表，格式为[(x1,y1), (x2,y2), ...]

        Note:
            紫色HSV阈值范围：H[130-160], S[80-255], V[80-255]
            该范围经过实验验证，适用于室内LED激光笔
        """
        # 步骤1: BGR转HSV颜色空间，HSV对光照变化更鲁棒
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # 步骤2: 设定紫色HSV阈值范围进行颜色过滤
        lower_purple = np.array([130, 80, 80])   # 紫色下界：色调130°，饱和度80，亮度80
        upper_purple = np.array([160, 255, 255]) # 紫色上界：色调160°，饱和度255，亮度255
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)  # 生成二值掩码

        # 步骤3: 形态学闭运算（先膨胀后腐蚀）消除噪声并填补空洞
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)

        # 步骤4: 轮廓检测，提取外部轮廓并简化轮廓点
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []  # 存储检测到的激光点坐标

        # 步骤5: 遍历每个轮廓，计算激光点中心坐标
        for cnt in contours_purple:
            # 计算轮廓的最小外接矩形，获取中心点坐标
            rect = cv2.minAreaRect(cnt)  # 返回((cx,cy), (w,h), angle)
            cx, cy = map(int, rect[0])   # 提取中心坐标并转为整数
            laser_points.append((cx, cy))  # 添加到激光点列表

            # 在原图上绘制激光点标记（紫色圆点）
            cv2.circle(img, (cx, cy), self.pixel_radius, (255, 0, 255), -1)  # BGR紫色填充圆
            # 添加文字标签
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        return img, laser_points

# --------------------------- 圆形轨迹点生成函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """
    在指定中心点生成圆形轨迹点序列

    基于三角函数生成均匀分布在圆周上的离散点坐标，用于机器人路径规划。

    数学原理：
    圆的参数方程：x = cx + r*cos(θ), y = cy + r*sin(θ)
    其中θ = 2π*i/n，i∈[0,n-1]，确保点均匀分布在圆周上

    Args:
        center (tuple): 圆心坐标 (cx, cy)，通常为校正后矩形的几何中心
        radius (int): 圆的半径，单位为像素，建议取值为矩形短边的1/3-1/2
        num_points (int): 圆周上的点数量，建议8-16个点，过多会增加计算负担

    Returns:
        list: 圆形轨迹点坐标列表，格式为[(x1,y1), (x2,y2), ...]
              点按逆时针方向排列，起始点在圆心右侧（θ=0°）

    Example:
        # 在(100,100)为中心，半径40的圆上生成12个点
        points = generate_circle_points((100, 100), 40, 12)
        # 结果：[(140,100), (134,120), (120,134), ...]

    Note:
        生成的坐标会转换为整数，可能存在1像素的舍入误差
    """
    circle_points = []  # 存储圆形轨迹点
    cx, cy = center     # 解包圆心坐标

    # 基于参数方程生成圆周上的均匀分布点
    for i in range(num_points):
        # 计算当前点的角度：θ = 2π*i/n，确保均匀分布
        angle = 2 * math.pi * i / num_points

        # 应用圆的参数方程计算坐标
        x = int(cx + radius * math.cos(angle))  # x = cx + r*cos(θ)
        y = int(cy + radius * math.sin(angle))  # y = cy + r*sin(θ)

        circle_points.append((x, y))  # 添加到轨迹点列表

    return circle_points

# --------------------------- 透视变换工具函数 ---------------------------
def perspective_transform(pts, target_width, target_height):
    """
    对四边形进行透视变换
    :param pts: 四边形顶点坐标 (4,2)
    :param target_width: 校正后宽度
    :param target_height: 校正后高度
    :return: 变换矩阵M和逆矩阵M_inv
    """
    # 顶点排序（左上→右上→右下→左下）
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    # 目标坐标
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)  # 逆矩阵用于映射回原图
    return M, M_inv, src_pts

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)
    else:
        print("串口初始化失败")
        exit()

    # 核心参数
    min_contour_area = 3000
    max_contour_area = 40000
    target_sides = 4
    
    # 透视变换与圆形参数
    corrected_width = 200    # 校正后矩形宽度
    corrected_height = 150   # 校正后矩形高度
    circle_radius = 40       # 校正后矩形内圆的半径
    circle_num_points = 12   # 圆周点数量

    while not app.need_exit():
        # 读取图像
        img = cam.read()
        img_cv = image.image2cv(img, ensure_bgr=True, copy=False)
        output = img_cv.copy()

        # 1. 矩形检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_contour_area < area < max_contour_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == target_sides:
                    quads.append((approx, area))

        # 按面积排序（外框在前，内框在后）
        quads.sort(key=lambda x: x[1], reverse=True)
        inner_quads = quads[1:]  # 只处理内框

        # 2. 处理内框：透视变换→画圆→映射回原图
        all_circle_points = []  # 存储所有映射回原图的圆轨迹点
        for approx, area in inner_quads:
            # 提取顶点
            pts = approx.reshape(4, 2).astype(np.float32)
            
            # 计算透视变换矩阵
            M, M_inv, src_pts = perspective_transform(
                pts, corrected_width, corrected_height
            )
            
            # 生成校正后矩形内的圆形轨迹（圆心为校正后矩形的中心）
            corrected_center = (corrected_width//2, corrected_height//2)
            corrected_circle = generate_circle_points(
                corrected_center, circle_radius, circle_num_points
            )
            
            # 将校正后的圆轨迹点映射回原图
            if M_inv is not None:
                # 格式转换为opencv需要的形状 (1, N, 2)
                corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                original_points = [(int(x), int(y)) for x, y in original_points]
                all_circle_points.extend(original_points)
                
                # 绘制映射回原图的轨迹点（红色）
                for (x, y) in original_points:
                    cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
            
            # 绘制内框轮廓和中心点（调试用）
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)
            M_moments = cv2.moments(approx)
            if M_moments["m00"] != 0:
                cx = int(M_moments["m10"] / M_moments["m00"])
                cy = int(M_moments["m01"] / M_moments["m00"])
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)

        # 3. 激光检测
        output, laser_points = laser_detector.detect(output)

        # 4. 串口发送数据
        # 发送内框圆轨迹点（格式：C,数量,x1,y1,x2,y2...）
        if all_circle_points:
            circle_data = f"C,{len(all_circle_points)}"
            for (x, y) in all_circle_points:
                circle_data += f",{x},{y}"
            micu_printf(circle_data)
        
        # 发送激光点
        if laser_points:
            laser_data = f"L,{len(laser_points)}"
            for (x, y) in laser_points:
                laser_data += f",{x},{y}"
            micu_printf(laser_data)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)