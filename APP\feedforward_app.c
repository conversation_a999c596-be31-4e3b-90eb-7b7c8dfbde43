#include "feedforward_app.h"
#include "bno08x_app.h"
#include "bno08x_hal.h"

// 全局变量定义
FeedforwardController_t ff_controller;
IMU_Data_t imu_data;

extern UART_HandleTypeDef huart1;

/**
 * @brief 前馈控制器初始化
 */
void feedforward_init(void)
{
    // 初始化补偿系数 (可根据实际调试调整)
    ff_controller.K_pitch = 0.8f;          // 俯仰补偿系数
    ff_controller.K_yaw = 1.5f;            // 偏航补偿系数
    ff_controller.K_centrifugal = 0.8f;    // 离心力补偿系数

    // 物理参数 (根据实际云台尺寸调整)
    ff_controller.gimbal_height = 0.20f;   // 云台高度20cm
    ff_controller.car_velocity = 0.0f;     // 初始速度为0

    // 滤波参数
    ff_controller.filter_alpha = 0.2f;     // 低通滤波系数

    // 运动状态检测阈值 (降低阈值提高敏感度)
    ff_controller.turn_threshold = 0.1f;   // 转弯检测阈值 (rad/s)
    ff_controller.accel_threshold = 0.2f;  // 加速度检测阈值 (m/s²)

    // 初始化状态
    ff_controller.initialized = 1;
    ff_controller.calibrated = 0;
    ff_controller.calibration_samples = 0;
    ff_controller.motion_state = MOTION_STATE_STATIC;

    // 清零补偿输出
    ff_controller.pitch_compensation = 0.0f;
    ff_controller.yaw_compensation = 0.0f;

    my_printf(&huart1, "前馈控制器初始化完成\r\n");
}

/**
 * @brief IMU偏置校准 (静止状态下调用)
 */
void feedforward_calibrate(void)
{
    const uint32_t CALIBRATION_SAMPLES = 100;

    if (!ff_controller.calibrated)
    {
        if (ff_controller.calibration_samples == 0)
        {
            // 开始校准
            ff_controller.bias_ax = ff_controller.bias_ay = ff_controller.bias_az = 0.0f;
            ff_controller.bias_wx = ff_controller.bias_wy = ff_controller.bias_wz = 0.0f;
            my_printf(&huart1, "开始IMU偏置校准...\r\n");
        }

        // 更新IMU数据
        if (feedforward_update_imu_data() == 0)
        {
            // 累加偏置
            ff_controller.bias_ax += imu_data.ax;
            ff_controller.bias_ay += imu_data.ay;
            ff_controller.bias_az += (imu_data.az - 9.81f); // 减去重力加速度
            ff_controller.bias_wx += imu_data.wx;
            ff_controller.bias_wy += imu_data.wy;
            ff_controller.bias_wz += imu_data.wz;

            ff_controller.calibration_samples++;

            if (ff_controller.calibration_samples >= CALIBRATION_SAMPLES)
            {
                // 计算平均偏置
                ff_controller.bias_ax /= CALIBRATION_SAMPLES;
                ff_controller.bias_ay /= CALIBRATION_SAMPLES;
                ff_controller.bias_az /= CALIBRATION_SAMPLES;
                ff_controller.bias_wx /= CALIBRATION_SAMPLES;
                ff_controller.bias_wy /= CALIBRATION_SAMPLES;
                ff_controller.bias_wz /= CALIBRATION_SAMPLES;

                ff_controller.calibrated = 1;
                my_printf(&huart1, "IMU偏置校准完成\r\n");
                my_printf(&huart1, "偏置: ax=%.3f ay=%.3f az=%.3f\r\n",
                         ff_controller.bias_ax, ff_controller.bias_ay, ff_controller.bias_az);
                my_printf(&huart1, "偏置: wx=%.3f wy=%.3f wz=%.3f\r\n",
                         ff_controller.bias_wx, ff_controller.bias_wy, ff_controller.bias_wz);
            }
        }
    }
}

/**
 * @brief 更新IMU数据
 * @return 0: 成功, -1: 失败
 */
int8_t feedforward_update_imu_data(void)
{
    // 检查BNO08X是否有新数据
    if (dataAvailable())
    {
        // 获取加速度数据 (需要先启用加速度计)
        imu_data.ax = getAccelX();
        imu_data.ay = getAccelY();
        imu_data.az = getAccelZ();

        // 获取陀螺仪数据 (需要先启用陀螺仪)
        imu_data.wx = getGyroX();
        imu_data.wy = getGyroY();
        imu_data.wz = getGyroZ();

        // 获取姿态角
        imu_data.roll = get_roll();
        imu_data.pitch = get_pitch();
        imu_data.yaw = get_yaw();

        imu_data.valid = 1;
        return 0;
    }

    imu_data.valid = 0;
    return -1;
}

/**
 * @brief 运动状态识别
 * @return 当前运动状态
 */
MotionState_t feedforward_identify_motion_state(void)
{
    if (!imu_data.valid) return MOTION_STATE_STATIC;

    float abs_wz = fabs(imu_data.wz - ff_controller.bias_wz);
    float abs_ax = fabs(imu_data.ax - ff_controller.bias_ax);

    if (abs_wz > ff_controller.turn_threshold)
    {
        return MOTION_STATE_TURNING;
    }
    else if (abs_ax > ff_controller.accel_threshold)
    {
        return MOTION_STATE_ACCELERATING;
    }
    else
    {
        return MOTION_STATE_STATIC;
    }
}

/**
 * @brief 前馈补偿计算
 */
void feedforward_calculate_compensation(void)
{
    if (!imu_data.valid || !ff_controller.calibrated)
    {
        ff_controller.pitch_compensation = 0.0f;
        ff_controller.yaw_compensation = 0.0f;
        return;
    }

    // 去除偏置
    float ax_corrected = imu_data.ax - ff_controller.bias_ax;
    float ay_corrected = imu_data.ay - ff_controller.bias_ay;
    float wz_corrected = imu_data.wz - ff_controller.bias_wz;

    // 低通滤波
    ff_controller.filtered_ax = ff_controller.filter_alpha * ax_corrected +
                               (1.0f - ff_controller.filter_alpha) * ff_controller.filtered_ax;
    ff_controller.filtered_wz = ff_controller.filter_alpha * wz_corrected +
                               (1.0f - ff_controller.filter_alpha) * ff_controller.filtered_wz;

    // 根据运动状态计算补偿
    switch (ff_controller.motion_state)
    {
        case MOTION_STATE_ACCELERATING:
            // 直线加速/减速补偿
            ff_controller.pitch_compensation = ff_controller.filtered_ax * ff_controller.K_pitch;
            ff_controller.yaw_compensation = 0.0f;
            break;

        case MOTION_STATE_TURNING:
            // 转弯补偿
            ff_controller.pitch_compensation = 0.0f;
            ff_controller.yaw_compensation = ff_controller.filtered_wz *
                                           ff_controller.gimbal_height * ff_controller.K_yaw;

            // 如果有速度信息，加上离心力补偿
            if (ff_controller.car_velocity > 0.1f)
            {
                float centrifugal_acc = ff_controller.car_velocity * fabs(ff_controller.filtered_wz);
                ff_controller.yaw_compensation += centrifugal_acc * ff_controller.K_centrifugal;
            }
            break;

        case MOTION_STATE_STATIC:
        default:
            ff_controller.pitch_compensation = 0.0f;
            ff_controller.yaw_compensation = 0.0f;
            break;
    }

    // 限幅保护
    ff_controller.pitch_compensation = pid_constrain(ff_controller.pitch_compensation, -30.0f, 30.0f);
    ff_controller.yaw_compensation = pid_constrain(ff_controller.yaw_compensation, -30.0f, 30.0f);
}

/**
 * @brief 前馈处理主函数
 */
void feedforward_process(void)
{
    if (!ff_controller.initialized) return;

    // 更新IMU数据
    feedforward_update_imu_data();

    // 如果未校准，执行校准
    if (!ff_controller.calibrated)
    {
        feedforward_calibrate();
        return;
    }

    // 识别运动状态
    ff_controller.motion_state = feedforward_identify_motion_state();

    // 临时调试：打印IMU数据和运动状态
    static uint32_t debug_counter = 0;
    if (++debug_counter % 100 == 0) // 每500ms打印一次
    {
        my_printf(&huart5, "IMU: ax=%.3f wz=%.3f State=%d Calib=%d\r\n",
                 imu_data.ax, imu_data.wz, ff_controller.motion_state, ff_controller.calibrated);
    }

    // 计算前馈补偿
    feedforward_calculate_compensation();

    // 临时测试：如果IMU数据有效但补偿为0，强制给一个小补偿测试
    if (imu_data.valid && ff_controller.calibrated &&
        ff_controller.pitch_compensation == 0.0f && ff_controller.yaw_compensation == 0.0f)
    {
        // 如果检测到轻微的角速度，给一个测试补偿
        if (fabs(imu_data.wz - ff_controller.bias_wz) > 0.05f)
        {
            ff_controller.yaw_compensation = (imu_data.wz - ff_controller.bias_wz) * 2.0f;
        }
    }
}

/**
 * @brief 获取前馈补偿值
 * @param pitch_comp 俯仰补偿输出
 * @param yaw_comp 偏航补偿输出
 */
void feedforward_get_compensation(float *pitch_comp, float *yaw_comp)
{
    if (pitch_comp) *pitch_comp = ff_controller.pitch_compensation;
    if (yaw_comp) *yaw_comp = ff_controller.yaw_compensation;
}

/**
 * @brief 设置补偿参数
 */
void feedforward_set_parameters(float k_pitch, float k_yaw, float k_centrifugal)
{
    ff_controller.K_pitch = k_pitch;
    ff_controller.K_yaw = k_yaw;
    ff_controller.K_centrifugal = k_centrifugal;
}

/**
 * @brief 设置物理参数
 */
void feedforward_set_physical_params(float height, float velocity)
{
    ff_controller.gimbal_height = height;
    ff_controller.car_velocity = velocity;
}

/**
 * @brief 前馈控制任务 (5ms周期调用)
 */
void feedforward_task(void)
{
    feedforward_process();
}

/**
 * @brief 打印前馈状态信息
 */
void feedforward_print_status(void)
{
    const char* state_names[] = {"静止", "直线", "转弯", "加速"};
    my_printf(&huart1, "前馈状态: %s, 校准: %s\r\n",
             state_names[ff_controller.motion_state],
             ff_controller.calibrated ? "完成" : "进行中");
}

/**
 * @brief 打印补偿值
 */
void feedforward_print_compensation(void)
{
    my_printf(&huart1, "前馈补偿: Pitch=%.2f°, Yaw=%.2f°\r\n",
             ff_controller.pitch_compensation, ff_controller.yaw_compensation);
}
