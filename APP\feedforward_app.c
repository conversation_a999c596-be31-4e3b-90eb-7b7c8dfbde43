#include "feedforward_app.h"
#include "bno08x_app.h"
#include "bno08x_hal.h"

// Global variables
FeedforwardController_t ff_controller;
IMU_Data_t imu_data;

extern UART_HandleTypeDef huart1;

/**
 * @brief Feedforward controller initialization
 */
void feedforward_init(void)
{
    // Initialize compensation coefficients
    ff_controller.K_pitch = 0.8f;          // Pitch compensation coefficient
    ff_controller.K_yaw = 1.2f;            // Yaw compensation coefficient  
    ff_controller.K_centrifugal = 0.5f;    // Centrifugal compensation coefficient
    
    // Physical parameters
    ff_controller.gimbal_height = 0.20f;   // Gimbal height 20cm
    ff_controller.car_velocity = 0.0f;     // Initial velocity 0
    
    // Filter parameters
    ff_controller.filter_alpha = 0.2f;     // Low-pass filter coefficient
    
    // Motion state detection thresholds
    ff_controller.turn_threshold = 0.3f;   // Turn detection threshold (rad/s)
    ff_controller.accel_threshold = 0.5f;  // Acceleration detection threshold (m/s虏)
    
    // Initialize state
    ff_controller.initialized = 1;
    ff_controller.calibrated = 0;
    ff_controller.calibration_samples = 0;
    ff_controller.motion_state = MOTION_STATE_STATIC;
    
    // Clear compensation output
    ff_controller.pitch_compensation = 0.0f;
    ff_controller.yaw_compensation = 0.0f;
    
    my_printf(&huart1, "Feedforward controller initialized\r\n");
}

/**
 * @brief IMU bias calibration (call when device is static)
 */
void feedforward_calibrate(void)
{
    const uint32_t CALIBRATION_SAMPLES = 100;
    
    if (!ff_controller.calibrated)
    {
        if (ff_controller.calibration_samples == 0)
        {
            // Start calibration
            ff_controller.bias_ax = ff_controller.bias_ay = ff_controller.bias_az = 0.0f;
            ff_controller.bias_wx = ff_controller.bias_wy = ff_controller.bias_wz = 0.0f;
            my_printf(&huart1, "Starting IMU bias calibration...\r\n");
        }
        
        // Update IMU data
        if (feedforward_update_imu_data() == 0)
        {
            // Accumulate bias
            ff_controller.bias_ax += imu_data.ax;
            ff_controller.bias_ay += imu_data.ay;
            ff_controller.bias_az += (imu_data.az - 9.81f); // Subtract gravity
            ff_controller.bias_wx += imu_data.wx;
            ff_controller.bias_wy += imu_data.wy;
            ff_controller.bias_wz += imu_data.wz;
            
            ff_controller.calibration_samples++;
            
            if (ff_controller.calibration_samples >= CALIBRATION_SAMPLES)
            {
                // Calculate average bias
                ff_controller.bias_ax /= CALIBRATION_SAMPLES;
                ff_controller.bias_ay /= CALIBRATION_SAMPLES;
                ff_controller.bias_az /= CALIBRATION_SAMPLES;
                ff_controller.bias_wx /= CALIBRATION_SAMPLES;
                ff_controller.bias_wy /= CALIBRATION_SAMPLES;
                ff_controller.bias_wz /= CALIBRATION_SAMPLES;
                
                ff_controller.calibrated = 1;
                my_printf(&huart1, "IMU bias calibration complete\r\n");
                my_printf(&huart1, "Bias: ax=%.3f ay=%.3f az=%.3f\r\n", 
                         ff_controller.bias_ax, ff_controller.bias_ay, ff_controller.bias_az);
                my_printf(&huart1, "Bias: wx=%.3f wy=%.3f wz=%.3f\r\n",
                         ff_controller.bias_wx, ff_controller.bias_wy, ff_controller.bias_wz);
            }
        }
    }
}

/**
 * @brief Update IMU data
 * @return 0: Success, -1: Failed
 */
int8_t feedforward_update_imu_data(void)
{
    // Check if BNO08X has new data
    if (dataAvailable())
    {
        // Get acceleration data (need to enable accelerometer first)
        imu_data.ax = getAccelX();
        imu_data.ay = getAccelY(); 
        imu_data.az = getAccelZ();
        
        // Get gyroscope data (need to enable gyroscope first)
        imu_data.wx = getGyroX();
        imu_data.wy = getGyroY();
        imu_data.wz = getGyroZ();
        
        // Get attitude angles
        imu_data.roll = get_roll();
        imu_data.pitch = get_pitch();
        imu_data.yaw = get_yaw();
        
        imu_data.valid = 1;
        return 0;
    }
    
    imu_data.valid = 0;
    return -1;
}

/**
 * @brief Motion state identification
 * @return Current motion state
 */
MotionState_t feedforward_identify_motion_state(void)
{
    if (!imu_data.valid) return MOTION_STATE_STATIC;
    
    float abs_wz = fabs(imu_data.wz - ff_controller.bias_wz);
    float abs_ax = fabs(imu_data.ax - ff_controller.bias_ax);
    
    if (abs_wz > ff_controller.turn_threshold)
    {
        return MOTION_STATE_TURNING;
    }
    else if (abs_ax > ff_controller.accel_threshold)
    {
        return MOTION_STATE_ACCELERATING;
    }
    else
    {
        return MOTION_STATE_STATIC;
    }
}

/**
 * @brief Feedforward compensation calculation
 */
void feedforward_calculate_compensation(void)
{
    if (!imu_data.valid || !ff_controller.calibrated)
    {
        ff_controller.pitch_compensation = 0.0f;
        ff_controller.yaw_compensation = 0.0f;
        return;
    }
    
    // Remove bias
    float ax_corrected = imu_data.ax - ff_controller.bias_ax;
    float ay_corrected = imu_data.ay - ff_controller.bias_ay;
    float wz_corrected = imu_data.wz - ff_controller.bias_wz;
    
    // Low-pass filter
    ff_controller.filtered_ax = ff_controller.filter_alpha * ax_corrected + 
                               (1.0f - ff_controller.filter_alpha) * ff_controller.filtered_ax;
    ff_controller.filtered_wz = ff_controller.filter_alpha * wz_corrected + 
                               (1.0f - ff_controller.filter_alpha) * ff_controller.filtered_wz;
    
    // Calculate compensation based on motion state
    switch (ff_controller.motion_state)
    {
        case MOTION_STATE_ACCELERATING:
            // Linear acceleration/deceleration compensation
            ff_controller.pitch_compensation = ff_controller.filtered_ax * ff_controller.K_pitch;
            ff_controller.yaw_compensation = 0.0f;
            break;
            
        case MOTION_STATE_TURNING:
            // Turn compensation
            ff_controller.pitch_compensation = 0.0f;
            ff_controller.yaw_compensation = ff_controller.filtered_wz * 
                                           ff_controller.gimbal_height * ff_controller.K_yaw;
            
            // Add centrifugal compensation if velocity is available
            if (ff_controller.car_velocity > 0.1f)
            {
                float centrifugal_acc = ff_controller.car_velocity * fabs(ff_controller.filtered_wz);
                ff_controller.yaw_compensation += centrifugal_acc * ff_controller.K_centrifugal;
            }
            break;
            
        case MOTION_STATE_STATIC:
        default:
            ff_controller.pitch_compensation = 0.0f;
            ff_controller.yaw_compensation = 0.0f;
            break;
    }
    
    // Limit protection
    ff_controller.pitch_compensation = pid_constrain(ff_controller.pitch_compensation, -30.0f, 30.0f);
    ff_controller.yaw_compensation = pid_constrain(ff_controller.yaw_compensation, -30.0f, 30.0f);
}

/**
 * @brief Feedforward processing main function
 */
void feedforward_process(void)
{
    if (!ff_controller.initialized) return;
    
    // Update IMU data
    feedforward_update_imu_data();
    
    // If not calibrated, perform calibration
    if (!ff_controller.calibrated)
    {
        feedforward_calibrate();
        return;
    }
    
    // Identify motion state
    ff_controller.motion_state = feedforward_identify_motion_state();
    
    // Calculate feedforward compensation
    feedforward_calculate_compensation();
}

/**
 * @brief Get feedforward compensation values
 * @param pitch_comp Pitch compensation output
 * @param yaw_comp Yaw compensation output
 */
void feedforward_get_compensation(float *pitch_comp, float *yaw_comp)
{
    if (pitch_comp) *pitch_comp = ff_controller.pitch_compensation;
    if (yaw_comp) *yaw_comp = ff_controller.yaw_compensation;
}

/**
 * @brief Set compensation parameters
 */
void feedforward_set_parameters(float k_pitch, float k_yaw, float k_centrifugal)
{
    ff_controller.K_pitch = k_pitch;
    ff_controller.K_yaw = k_yaw;
    ff_controller.K_centrifugal = k_centrifugal;
}

/**
 * @brief Set physical parameters
 */
void feedforward_set_physical_params(float height, float velocity)
{
    ff_controller.gimbal_height = height;
    ff_controller.car_velocity = velocity;
}

/**
 * @brief Feedforward control task (5ms period)
 */
void feedforward_task(void)
{
    feedforward_process();
}

/**
 * @brief Print feedforward status information
 */
void feedforward_print_status(void)
{
    const char* state_names[] = {"STATIC", "STRAIGHT", "TURNING", "ACCEL"};
    my_printf(&huart1, "FF State: %s, Calibrated: %s\r\n", 
             state_names[ff_controller.motion_state],
             ff_controller.calibrated ? "YES" : "NO");
}

/**
 * @brief Print compensation values
 */
void feedforward_print_compensation(void)
{
    my_printf(&huart1, "FF Compensation: Pitch=%.2f, Yaw=%.2f\r\n",
             ff_controller.pitch_compensation, ff_controller.yaw_compensation);
}
