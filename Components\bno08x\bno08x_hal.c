#include "bno08x_hal.h"
#include <math.h>
#include <string.h>
#include "main.h"

extern I2C_HandleTypeDef hi2c1;
extern UART_HandleTypeDef huart1;

/** @defgroup BNO080_Variables BNO080 Variables
 * @{
 */

// Private function prototypes
static uint8_t receivePacket(void);
static uint8_t sendPacket(uint8_t channelNumber, uint8_t dataLength);

/** I2C handle for BNO080 communication */
static I2C_HandleTypeDef *hi2c_bno080;

/** I2C device address (7-bit) */
static uint8_t _deviceAddress;

/** SHTP header buffer (4 bytes) */
static uint8_t shtpHeader[4];

/** SHTP data buffer */
static uint8_t shtpData[MAX_PACKET_SIZE];

/** Sequence numbers for each SHTP channel */
static uint8_t sequenceNumber[6] = {0, 0, 0, 0, 0, 0};

/** Command sequence number */
static uint8_t commandSequenceNumber = 0;

/** FRS metadata buffer */
static uint32_t metaData[MAX_METADATA_SIZE];

/** Sensor data variables */
static uint16_t rawAccelX, rawAccelY, rawAccelZ;            // Raw accelerometer data
static uint8_t accelAccuracy;                               // Accelerometer accuracy
static uint16_t rawLinAccelX, rawLinAccelY, rawLinAccelZ;   // Raw linear acceleration data
static uint8_t accelLinAccuracy;                            // Linear acceleration accuracy
static uint16_t rawGyroX, rawGyroY, rawGyroZ;               // Raw gyroscope data
static uint8_t gyroAccuracy;                                // Gyroscope accuracy
static uint16_t rawMagX, rawMagY, rawMagZ;                  // Raw magnetometer data
static uint8_t magAccuracy;                                 // Magnetometer accuracy
static uint16_t rawQuatI, rawQuatJ, rawQuatK, rawQuatReal;  // Raw quaternion data
static uint16_t rawQuatRadianAccuracy;                      // Quaternion accuracy
static uint8_t quatAccuracy;                                // Quaternion accuracy
static uint16_t stepCount;                                  // Step counter
static uint8_t stabilityClassifier;                         // Stability classifier
static uint8_t activityClassifier;                          // Activity classifier
static uint8_t activityConfidences[10];                     // Activity confidences
static uint8_t *_activityConfidences = activityConfidences; // Activity confidences pointer

/** Q point values for different sensors */
static int rotationVector_Q1 = 14;      // Rotation vector Q point
static int accelerometer_Q1 = 8;        // Accelerometer Q point
static int linear_accelerometer_Q1 = 8; // Linear accelerometer Q point
static int gyro_Q1 = 9;                 // Gyroscope Q point
static int magnetometer_Q1 = 4;         // Magnetometer Q point

/**
 * @}
 */

/** @defgroup BNO080_Functions BNO080 Functions
 * @{
 */

/**
 * @brief Hardware reset for BNO08X sensor
 * @note Uses nRESET pin control and waits for Product ID Response
 * @retval 0: Success, -1: Failed
 */
int8_t BNO080_HardwareReset(void)
{
    uint32_t timeout = 0;
    uint8_t resetComplete = 0;
    uint8_t attempts = 0;

    // Hardware reset process
    extern UART_HandleTypeDef huart1;
    my_printf(&huart1, "BNO080: Starting hardware reset...\n");

    // 0. Initialize nRESET pin to high state
    HAL_GPIO_WritePin(BNO_RST_GPIO_Port, BNO_RST_Pin, GPIO_PIN_SET);
    HAL_Delay(10);

    // 1. Clear I2C buffer
    for (int i = 0; i < 5; i++)
    {
        if (receivePacket() == 0)
            break;
        HAL_Delay(1);
    }

    // 2. Execute hardware reset
    HAL_GPIO_WritePin(BNO_RST_GPIO_Port, BNO_RST_Pin, GPIO_PIN_RESET);
    HAL_Delay(50);

    HAL_GPIO_WritePin(BNO_RST_GPIO_Port, BNO_RST_Pin, GPIO_PIN_SET);
    HAL_Delay(200);

    // 3. Wait for sensor startup completion
    timeout = HAL_GetTick();
    uint8_t startup_packets_received = 0;
    uint8_t command_responses_received = 0;

    while ((HAL_GetTick() - timeout) < 3000)
    {
        if (receivePacket() == 1)
        {
            attempts++;

            if (shtpData[0] == SHTP_REPORT_PRODUCT_ID_RESPONSE)
            {
                resetComplete = 1;
                my_printf(&huart1, "BNO080: Product ID received, reset complete\n");
                break;
            }
            else if (shtpData[0] == SHTP_REPORT_COMMAND_RESPONSE)
            {
                command_responses_received++;

                if (command_responses_received >= 3)
                {
                    my_printf(&huart1, "BNO080: Requesting Product ID actively...\n");
                    // Request Product ID actively
                    shtpData[0] = SHTP_REPORT_PRODUCT_ID_REQUEST;
                    shtpData[1] = 0;
                    sendPacket(CHANNEL_CONTROL, 2);
                    HAL_Delay(100);

                    // Try to receive Product ID Response
                    for (int retry = 0; retry < 5; retry++)
                    {
                        if (receivePacket() == 1)
                        {
                            if (shtpData[0] == SHTP_REPORT_PRODUCT_ID_RESPONSE)
                            {
                                resetComplete = 1;
                                my_printf(&huart1, "BNO080: Active request successful\n");
                                break;
                            }
                        }
                        HAL_Delay(50);
                    }

                    if (resetComplete)
                        break;
                }
            }
            else
            {
                startup_packets_received++;
            }

            // Fallback: consider sensor started if enough packets received
            if (startup_packets_received > 10 && command_responses_received > 0)
            {
                resetComplete = 1;
                break;
            }
        }
        HAL_Delay(20);
    }

    if (!resetComplete)
    {
        my_printf(&huart1, "BNO080: Hardware reset failed after %d attempts\n", attempts);
        return -1; // Reset failed
    }

    // 4. Clear remaining packets in buffer
    int cleared_packets = 0;
    while (receivePacket() == 1)
    {
        cleared_packets++;
        HAL_Delay(1);
        if (cleared_packets > 10)
            break;
    }

    my_printf(&huart1, "BNO080: Hardware reset successful\n");
    return 0; // Success
}

/**
 * @brief Initialize BNO080 sensor
 * @param hi2c I2C handle from STM32 HAL
 * @param address I2C device address (7-bit, typically 0x4B)
 */
void BNO080_Init(I2C_HandleTypeDef *hi2c, uint8_t address)
{
    hi2c_bno080 = hi2c;
    _deviceAddress = address;
}

/**
 * @brief Software reset
 * @note Sends software reset command to sensor
 */
void softReset(void)
{
    shtpData[0] = 1;
    sendPacket(CHANNEL_EXECUTABLE, 1);
    HAL_Delay(50);
    while (receivePacket() == 1)
        ;
    HAL_Delay(50);
    while (receivePacket() == 1)
        ;
}

/**
 * @brief Get reset reason
 * @return Reset reason code, 0 if failed
 */
uint8_t resetReason(void)
{
    shtpData[0] = SHTP_REPORT_PRODUCT_ID_REQUEST;
    shtpData[1] = 0;
    sendPacket(CHANNEL_CONTROL, 2);
    if (receivePacket() == 1)
    {
        if (shtpData[0] == SHTP_REPORT_PRODUCT_ID_RESPONSE)
        {
            return shtpData[1];
        }
    }
    return 0;
}

/**
 * @brief Convert Q point fixed point to float
 * @param fixedPointValue Fixed point value
 * @param qPoint Q point value
 * @return Converted float value
 */
float qToFloat(int16_t fixedPointValue, uint8_t qPoint)
{
    float qFloat = (float)fixedPointValue;
    qFloat *= powf(2.0f, -qPoint);
    return qFloat;
}

/**
 * @brief Check if new data is available
 * @return 1 if data available, 0 otherwise
 */
uint8_t dataAvailable(void)
{
    if (receivePacket() == 1)
    {
        if (shtpHeader[2] == CHANNEL_REPORTS && shtpData[0] == SHTP_REPORT_BASE_TIMESTAMP)
        {
            parseInputReport();
            return 1;
        }
    }
    return 0;
}

/**
 * @brief Parse input report
 * @note Parses sensor data based on report ID
 */
void parseInputReport(void)
{
    int dataLength = ((uint16_t)shtpHeader[1] << 8 | shtpHeader[0]) & ~(1 << 15);
    dataLength -= 4;

    uint8_t status = shtpData[5 + 2] & 0x03;
    uint16_t data1 = (uint16_t)shtpData[5 + 5] << 8 | shtpData[5 + 4];
    uint16_t data2 = (uint16_t)shtpData[5 + 7] << 8 | shtpData[5 + 6];
    uint16_t data3 = (uint16_t)shtpData[5 + 9] << 8 | shtpData[5 + 8];
    uint16_t data4 = (dataLength - 5 > 9) ? ((uint16_t)shtpData[5 + 11] << 8 | shtpData[5 + 10]) : 0;
    uint16_t data5 = (dataLength - 5 > 11) ? ((uint16_t)shtpData[5 + 13] << 8 | shtpData[5 + 12]) : 0;

    switch (shtpData[5])
    {
    case SENSOR_REPORTID_ACCELEROMETER:
        accelAccuracy = status;
        rawAccelX = data1;
        rawAccelY = data2;
        rawAccelZ = data3;
        break;
    case SENSOR_REPORTID_LINEAR_ACCELERATION:
        accelLinAccuracy = status;
        rawLinAccelX = data1;
        rawLinAccelY = data2;
        rawLinAccelZ = data3;
        break;
    case SENSOR_REPORTID_GYROSCOPE:
        gyroAccuracy = status;
        rawGyroX = data1;
        rawGyroY = data2;
        rawGyroZ = data3;
        break;
    case SENSOR_REPORTID_MAGNETIC_FIELD:
        magAccuracy = status;
        rawMagX = data1;
        rawMagY = data2;
        rawMagZ = data3;
        break;
    case SENSOR_REPORTID_ROTATION_VECTOR:
    case SENSOR_REPORTID_GAME_ROTATION_VECTOR:
        quatAccuracy = status;
        rawQuatI = data1;
        rawQuatJ = data2;
        rawQuatK = data3;
        rawQuatReal = data4;
        rawQuatRadianAccuracy = data5;
        break;
    case SENSOR_REPORTID_STEP_COUNTER:
        stepCount = data3;
        break;
    case SENSOR_REPORTID_STABILITY_CLASSIFIER:
        stabilityClassifier = shtpData[5 + 4];
        break;
    case SENSOR_REPORTID_PERSONAL_ACTIVITY_CLASSIFIER:
        activityClassifier = shtpData[5 + 5];
        for (uint8_t x = 0; x < 9; x++)
        {
            _activityConfidences[x] = shtpData[5 + 6 + x];
        }
        break;
    default:
        break;
    }
}

// Quaternion getter functions
float getQuatI(void) { return qToFloat(rawQuatI, rotationVector_Q1); }
float getQuatJ(void) { return qToFloat(rawQuatJ, rotationVector_Q1); }
float getQuatK(void) { return qToFloat(rawQuatK, rotationVector_Q1); }
float getQuatReal(void) { return qToFloat(rawQuatReal, rotationVector_Q1); }
float getQuatRadianAccuracy(void) { return qToFloat(rawQuatRadianAccuracy, rotationVector_Q1); }
uint8_t getQuatAccuracy(void) { return quatAccuracy; }

// Accelerometer getter functions
float getAccelX(void) { return qToFloat(rawAccelX, accelerometer_Q1); }
float getAccelY(void) { return qToFloat(rawAccelY, accelerometer_Q1); }
float getAccelZ(void) { return qToFloat(rawAccelZ, accelerometer_Q1); }
uint8_t getAccelAccuracy(void) { return accelAccuracy; }

// Linear accelerometer getter functions
float getLinAccelX(void) { return qToFloat(rawLinAccelX, linear_accelerometer_Q1); }
float getLinAccelY(void) { return qToFloat(rawLinAccelY, linear_accelerometer_Q1); }
float getLinAccelZ(void) { return qToFloat(rawLinAccelZ, linear_accelerometer_Q1); }
uint8_t getLinAccelAccuracy(void) { return accelLinAccuracy; }

// Gyroscope getter functions
float getGyroX(void) { return qToFloat(rawGyroX, gyro_Q1); }
float getGyroY(void) { return qToFloat(rawGyroY, gyro_Q1); }
float getGyroZ(void) { return qToFloat(rawGyroZ, gyro_Q1); }
uint8_t getGyroAccuracy(void) { return gyroAccuracy; }

// Magnetometer getter functions
float getMagX(void) { return qToFloat(rawMagX, magnetometer_Q1); }
float getMagY(void) { return qToFloat(rawMagY, magnetometer_Q1); }
float getMagZ(void) { return qToFloat(rawMagZ, magnetometer_Q1); }
uint8_t getMagAccuracy(void) { return magAccuracy; }

// Other sensor getter functions
uint16_t getStepCount(void) { return stepCount; }
uint8_t getStabilityClassifier(void) { return stabilityClassifier; }
uint8_t getActivityClassifier(void) { return activityClassifier; }

// Calibration functions
void calibrateAccelerometer(void) { sendCalibrateCommand(CALIBRATE_ACCEL); }
void calibrateGyro(void) { sendCalibrateCommand(CALIBRATE_GYRO); }
void calibrateMagnetometer(void) { sendCalibrateCommand(CALIBRATE_MAG); }
void calibratePlanarAccelerometer(void) { sendCalibrateCommand(CALIBRATE_PLANAR_ACCEL); }
void calibrateAll(void) { sendCalibrateCommand(CALIBRATE_ACCEL_GYRO_MAG); }
void endCalibration(void) { sendCalibrateCommand(CALIBRATE_STOP); }

void saveCalibration(void)
{
    for (uint8_t x = 3; x < 12; x++)
    {
        shtpData[x] = 0;
    }
    sendCommand(COMMAND_DCD);
}

/**
 * @brief Set feature command
 * @param reportID Report ID (e.g., SENSOR_REPORTID_ROTATION_VECTOR)
 * @param timeBetweenReports Time between reports in milliseconds
 * @param specificConfig Specific configuration (usually 0)
 */
void setFeatureCommand(uint8_t reportID, uint32_t timeBetweenReports, uint32_t specificConfig)
{
    uint32_t microsBetweenReports = timeBetweenReports * 1000;
    shtpData[0] = SHTP_REPORT_SET_FEATURE_COMMAND;
    shtpData[1] = reportID;
    shtpData[2] = 0;
    shtpData[3] = 0;
    shtpData[4] = 0;
    shtpData[5] = (microsBetweenReports >> 0) & 0xFF;
    shtpData[6] = (microsBetweenReports >> 8) & 0xFF;
    shtpData[7] = (microsBetweenReports >> 16) & 0xFF;
    shtpData[8] = (microsBetweenReports >> 24) & 0xFF;
    shtpData[9] = 0;
    shtpData[10] = 0;
    shtpData[11] = 0;
    shtpData[12] = 0;
    shtpData[13] = (specificConfig >> 0) & 0xFF;
    shtpData[14] = (specificConfig >> 8) & 0xFF;
    shtpData[15] = (specificConfig >> 16) & 0xFF;
    shtpData[16] = (specificConfig >> 24) & 0xFF;
    sendPacket(CHANNEL_CONTROL, 17);
}

/**
 * @brief Send command
 * @param command Command ID (e.g., COMMAND_DCD)
 */
void sendCommand(uint8_t command)
{
    shtpData[0] = SHTP_REPORT_COMMAND_REQUEST;
    shtpData[1] = commandSequenceNumber++;
    shtpData[2] = command;
    for (uint8_t i = 3; i < 12; i++)
    {
        shtpData[i] = 0;
    }
    sendPacket(CHANNEL_CONTROL, 12);
}

/**
 * @brief Send calibrate command
 * @param thingToCalibrate What to calibrate (e.g., CALIBRATE_ACCEL)
 */
void sendCalibrateCommand(uint8_t thingToCalibrate)
{
    for (uint8_t x = 3; x < 12; x++)
    {
        shtpData[x] = 0;
    }
    switch (thingToCalibrate)
    {
    case CALIBRATE_ACCEL:
        shtpData[3] = 1;
        break;
    case CALIBRATE_GYRO:
        shtpData[4] = 1;
        break;
    case CALIBRATE_MAG:
        shtpData[5] = 1;
        break;
    case CALIBRATE_PLANAR_ACCEL:
        shtpData[7] = 1;
        break;
    case CALIBRATE_ACCEL_GYRO_MAG:
        shtpData[3] = 1;
        shtpData[4] = 1;
        shtpData[5] = 1;
        break;
    case CALIBRATE_STOP:
        break;
    default:
        break;
    }
    sendCommand(COMMAND_ME_CALIBRATE);
}

// Enable sensor functions
void enableRotationVector(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_ROTATION_VECTOR, timeBetweenReports, 0); }
void enableGameRotationVector(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_GAME_ROTATION_VECTOR, timeBetweenReports, 0); }
void enableAccelerometer(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_ACCELEROMETER, timeBetweenReports, 0); }
void enableLinearAccelerometer(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_LINEAR_ACCELERATION, timeBetweenReports, 0); }
void enableGyro(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_GYROSCOPE, timeBetweenReports, 0); }
void enableMagnetometer(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_MAGNETIC_FIELD, timeBetweenReports, 0); }
void enableStepCounter(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_STEP_COUNTER, timeBetweenReports, 0); }
void enableStabilityClassifier(uint32_t timeBetweenReports) { setFeatureCommand(SENSOR_REPORTID_STABILITY_CLASSIFIER, timeBetweenReports, 0); }

// FRS (Flash Record System) functions
int16_t getQ1(uint16_t recordID)
{
    if (readFRSdata(recordID, 7, 1))
    {
        return (int16_t)(metaData[0] & 0xFFFF);
    }
    return 0;
}

int16_t getQ2(uint16_t recordID)
{
    if (readFRSdata(recordID, 8, 1))
    {
        return (int16_t)(metaData[0] >> 16);
    }
    return 0;
}

int16_t getQ3(uint16_t recordID)
{
    if (readFRSdata(recordID, 9, 1))
    {
        return (int16_t)(metaData[0] & 0xFFFF);
    }
    return 0;
}

float getResolution(uint16_t recordID)
{
    if (readFRSdata(recordID, 4, 1))
    {
        uint32_t res = metaData[0];
        return *(float *)&res;
    }
    return 0.0f;
}

float getRange(uint16_t recordID)
{
    if (readFRSdata(recordID, 2, 1))
    {
        uint32_t range = metaData[0];
        return *(float *)&range;
    }
    return 0.0f;
}

uint32_t readFRSword(uint16_t recordID, uint8_t wordNumber)
{
    if (readFRSdata(recordID, wordNumber, 1))
    {
        return metaData[0];
    }
    return 0;
}

void frsReadRequest(uint16_t recordID, uint16_t readOffset, uint16_t blockSize)
{
    shtpData[0] = SHTP_REPORT_FRS_READ_REQUEST;
    shtpData[1] = 0;
    shtpData[2] = recordID & 0xFF;
    shtpData[3] = recordID >> 8;
    shtpData[4] = readOffset & 0xFF;
    shtpData[5] = readOffset >> 8;
    shtpData[6] = blockSize & 0xFF;
    shtpData[7] = blockSize >> 8;
    sendPacket(CHANNEL_CONTROL, 8);
}

uint8_t readFRSdata(uint16_t recordID, uint8_t startLocation, uint8_t wordsToRead)
{
    frsReadRequest(recordID, startLocation, wordsToRead);
    HAL_Delay(10);
    for (uint8_t attempts = 0; attempts < 10; attempts++)
    {
        if (receivePacket() == 1)
        {
            if (shtpHeader[2] == CHANNEL_CONTROL && shtpData[0] == SHTP_REPORT_FRS_READ_RESPONSE)
            {
                uint16_t readRecordID = (uint16_t)shtpData[3] << 8 | shtpData[2];
                if (readRecordID != recordID)
                    continue;
                // uint8_t dataOffset = shtpData[5] & 0x0F; // Reserved for future use
                for (uint8_t i = 0; i < wordsToRead; i++)
                {
                    uint8_t index = 6 + (i * 4);
                    if (index + 3 < MAX_PACKET_SIZE)
                    {
                        metaData[i] = ((uint32_t)shtpData[index + 3] << 24) |
                                      ((uint32_t)shtpData[index + 2] << 16) |
                                      ((uint32_t)shtpData[index + 1] << 8) |
                                      shtpData[index];
                    }
                }
                return 1;
            }
        }
        HAL_Delay(5);
    }
    return 0;
}

/**
 * @brief Receive SHTP packet
 * @return 1 if packet received, 0 otherwise
 */
static uint8_t receivePacket(void)
{
    uint8_t header[4];
    if (HAL_I2C_Master_Receive(hi2c_bno080, _deviceAddress << 1, header, 4, HAL_MAX_DELAY) != HAL_OK)
    {
        return 0;
    }

    uint16_t packetLength = ((uint16_t)header[1] << 8 | header[0]) & ~(1 << 15);
    if (packetLength <= 4)
    {
        memcpy(shtpHeader, header, 4);
        return 1;
    }

    uint16_t dataLength = packetLength - 4;
    memcpy(shtpHeader, header, 4);

    uint16_t bytesRemaining = dataLength;
    uint16_t dataSpot = 0;

    while (bytesRemaining > 0)
    {
        uint16_t numberOfBytesToRead = (bytesRemaining > (I2C_BUFFER_LENGTH - 4)) ? (I2C_BUFFER_LENGTH - 4) : bytesRemaining;
        uint8_t temp[I2C_BUFFER_LENGTH];
        if (HAL_I2C_Master_Receive(hi2c_bno080, _deviceAddress << 1, temp, numberOfBytesToRead + 4, HAL_MAX_DELAY) != HAL_OK)
        {
            return 0;
        }
        for (uint8_t x = 0; x < numberOfBytesToRead; x++)
        {
            if (dataSpot < MAX_PACKET_SIZE)
            {
                shtpData[dataSpot++] = temp[x + 4];
            }
        }
        bytesRemaining -= numberOfBytesToRead;
    }
    return 1;
}

/**
 * @brief Send SHTP packet
 * @param channelNumber Channel number (e.g., CHANNEL_CONTROL)
 * @param dataLength Length of data to send
 * @return 1 if packet sent, 0 otherwise
 */
static uint8_t sendPacket(uint8_t channelNumber, uint8_t dataLength)
{
    uint16_t packetLength = dataLength + 4;
    uint8_t packet[packetLength];

    packet[0] = packetLength & 0xFF;
    packet[1] = (packetLength >> 8) & 0xFF;
    packet[2] = channelNumber;
    packet[3] = sequenceNumber[channelNumber]++;

    for (uint8_t i = 0; i < dataLength; i++)
    {
        packet[4 + i] = shtpData[i];
    }

    if (HAL_I2C_Master_Transmit(hi2c_bno080, _deviceAddress << 1, packet, packetLength, HAL_MAX_DELAY) != HAL_OK)
    {
        return 0;
    }
    return 1;
}

#define M_PI_F 3.14159265358979323846f // Use float version to avoid precision warnings
/**
 * @brief Convert quaternion to Euler angles
 * @param quatI Quaternion I component (x)
 * @param quatJ Quaternion J component (y)
 * @param quatK Quaternion K component (z)
 * @param quatReal Quaternion real component (w)
 * @param roll Output roll angle in degrees
 * @param pitch Output pitch angle in degrees
 * @param yaw Output yaw angle in degrees
 */
void QuaternionToEulerAngles(float quatI, float quatJ, float quatK, float quatReal,
                             float *roll, float *pitch, float *yaw)
{
    // Normalize quaternion
    float norm = sqrtf(quatI * quatI + quatJ * quatJ + quatK * quatK + quatReal * quatReal);
    quatI /= norm;
    quatJ /= norm;
    quatK /= norm;
    quatReal /= norm;

    // Convert to Euler angles
    // Pitch (y-axis rotation)
    float sinp = 2.0f * (quatReal * quatJ - quatK * quatI);
    if (fabsf(sinp) >= 1)
        *pitch = (sinp >= 0 ? 1 : -1) * (M_PI_F / 2.0f) * 180.0f / M_PI_F;
    else
        *pitch = asinf(sinp) * 180.0f / M_PI_F;

    // Roll (x-axis rotation)
    float sinr_cosp = 2.0f * (quatReal * quatI + quatJ * quatK);
    float cosr_cosp = 1.0f - 2.0f * (quatI * quatI + quatJ * quatJ);
    *roll = atan2f(sinr_cosp, cosr_cosp) * 180.0f / M_PI_F;

    // Yaw (z-axis rotation)
    float siny_cosp = 2.0f * (quatReal * quatK + quatI * quatJ);
    float cosy_cosp = 1.0f - 2.0f * (quatJ * quatJ + quatK * quatK);
    *yaw = atan2f(siny_cosp, cosy_cosp) * 180.0f / M_PI_F;
}
