#include "bno08x_app.h"
#include "bno08x_hal.h"

extern UART_HandleTypeDef huart1;
extern I2C_HandleTypeDef hi2c1;

float roll, pitch, yaw;
float convert_to_continuous_yaw(float current_yaw);

/**
 * @brief BNO080 sensor complete initialization and configuration function
 * @note Includes hardware initialization, reset, sensor configuration
 * @note User can selectively enable different function modules via comments
 * @retval 0: Success, -1: Failed
 */
int8_t my_bno080_init(void)
{
    my_printf(&huart5, "BNO080: Initializing sensor...\n");

    // 1. Hardware initialization
    BNO080_Init(&hi2c1, BNO080_DEFAULT_ADDRESS);

    // 2. Hardware reset (recommended for reliability)
    if (BNO080_HardwareReset() == 0)
    {
        // Hardware reset successful
    }
    else
    {
        my_printf(&huart5, "BNO080: Hardware reset failed, trying software reset\n");
        // Fallback: software reset
        softReset();
        HAL_Delay(100);
    }

    // 3. Enable rotation vector (quaternion/Euler angles) - 使用更长的间隔
    my_printf(&huart5, "BNO080: Enabling rotation vector...\n");
    enableRotationVector(200); // Enable rotation vector, 200ms interval (降低频率)
    HAL_Delay(200); // 等待传感器响应

    // 启用前馈控制所需的传感器 - 使用更长的间隔
    my_printf(&huart5, "BNO080: Enabling accelerometer...\n");
    enableAccelerometer(100);          // 启用加速度计, 100ms间隔 (降低频率)
    HAL_Delay(200);

    my_printf(&huart5, "BNO080: Enabling gyroscope...\n");
    enableGyro(100);                   // 启用陀螺仪, 100ms间隔 (降低频率)
    HAL_Delay(200);
    // enableMagnetometer(100);          // Enable magnetometer, 100ms interval
    // enableLinearAccelerometer(50);    // Enable linear accelerometer, 50ms interval
    // enableStepCounter(1000);          // Enable step counter, 1000ms interval

    // 4. Advanced features (optional)
    // enableGameRotationVector(20);     // Enable game rotation vector, 20ms interval
    // enableStabilityClassifier(500);   // Enable stability classifier, 500ms interval

    HAL_Delay(200); // Wait for sensor stabilization
    my_printf(&huart5, "BNO080: Initialization complete\n");
    return 0; // Success
}

uint8_t first_flat = 0;
float frist_yaw = 0;
/**
 * @brief BNO080 sensor data reading task
 * @note Should be called periodically every 50-100ms
 * @note Reads quaternion data and converts to Euler angles
 */
void bno080_task(void)
{
    // 调试计数器和无数据计数器
    static uint32_t debug_counter = 0;
    static uint32_t no_data_counter = 0;
    debug_counter++;

    // Check if new data is available
    if (dataAvailable())
    {
        no_data_counter = 0;  // 重置无数据计数器
        // 可选：数据可用调试 (可注释掉)
        // if (debug_counter % 500 == 0)
        // {
        //     my_printf(&huart5, "BNO080 data available!\r\n");
        // }
        // 1. Quaternion and attitude data (basic functionality)
        float quat_i = getQuatI();
        float quat_j = getQuatJ();
        float quat_k = getQuatK();
        float quat_real = getQuatReal();

        // 可选：四元数调试 (可注释掉)
        // if (debug_counter % 1000 == 0)
        // {
        //     my_printf(&huart5, "Quat: I=%.6f J=%.6f K=%.6f Real=%.6f\r\n",
        //              quat_i, quat_j, quat_k, quat_real);
        // }

        QuaternionToEulerAngles(quat_i, quat_j, quat_k, quat_real, &roll, &pitch, &yaw);
        if (first_flat == 0)
        {
            first_flat = 1;
            frist_yaw = yaw;
        }
        yaw = yaw - frist_yaw;
        my_printf(&huart5, "Euler: %.2f, %.2f, %.2f\r\n", roll, pitch, yaw);

////			 2. Accelerometer data (optional)
//			 float accel_x = getAccelX();
//			 float accel_y = getAccelY();
//			 float accel_z = getAccelZ();
//			 my_printf(&huart1, "Accel: %.3f, %.3f, %.3f g\n", accel_x, accel_y, accel_z);

//////			 3. Gyroscope data (optional)
//			 float gyro_x = getGyroX();
//			 float gyro_y = getGyroY();
//			 float gyro_z = getGyroZ();
//			 my_printf(&huart1, "Gyro: %.3f, %.3f, %.3f rad/s\n", gyro_x, gyro_y, gyro_z);

//////			 4. Magnetometer data (optional)
//			 float mag_x = getMagX();
//			 float mag_y = getMagY();
//			 float mag_z = getMagZ();
//			 my_printf(&huart1, "Mag: %.1f, %.1f, %.1f uT\n", mag_x, mag_y, mag_z);

//////			 5. Accuracy information (optional)
//			 uint8_t quat_accuracy = getQuatAccuracy();
//			 uint8_t accel_accuracy = getAccelAccuracy();
//			 uint8_t gyro_accuracy = getGyroAccuracy();
//			 uint8_t mag_accuracy = getMagAccuracy();
//			 my_printf(&huart1, "Accuracy: Q=%d A=%d G=%d M=%d\n",
//								 quat_accuracy, accel_accuracy, gyro_accuracy, mag_accuracy);

////			 6. Other sensor data (optional)
//			 uint16_t steps = getStepCount();
//			 uint8_t stability = getStabilityClassifier();
//			 my_printf(&huart1, "Steps: %d, Stability: %d\n", steps, stability);
    }
    else
    {
        no_data_counter++;

        // 传感器恢复机制：根据无数据时间采取不同措施
        if (no_data_counter % 1000 == 0)  // 每5秒检查一次
        {
            uint32_t no_data_seconds = no_data_counter / 200;  // 假设5ms调用一次
            my_printf(&huart5, "BNO080 no data for %ds - attempting recovery\r\n", no_data_seconds);

            if (no_data_seconds < 10)
            {
                // 前10秒：重新启用传感器，尝试不同的间隔
                my_printf(&huart5, "Re-enabling with different intervals...\r\n");
                enableRotationVector(100);  // 尝试更短的间隔
                HAL_Delay(100);
                enableAccelerometer(50);    // 尝试更短的间隔
                HAL_Delay(100);
                enableGyro(50);            // 尝试更短的间隔
                HAL_Delay(100);
                my_printf(&huart5, "Sensors re-enabled with 50-100ms intervals\r\n");
            }
            else if (no_data_seconds < 20)
            {
                // 10-20秒：检查I2C并重新初始化
                extern I2C_HandleTypeDef hi2c1;
                HAL_StatusTypeDef i2c_status = HAL_I2C_IsDeviceReady(&hi2c1, BNO080_DEFAULT_ADDRESS << 1, 3, 100);
                my_printf(&huart5, "I2C Status: %d - full reinit\r\n", i2c_status);
                my_bno080_init();
            }
            else
            {
                // 超过20秒：硬件复位
                my_printf(&huart5, "Hardware reset attempt\r\n");
                BNO080_HardwareReset();
                HAL_Delay(500);
                my_bno080_init();
                no_data_counter = 0;  // 重置计数器
            }
        }
    }
}

float get_roll(void)
{
    return roll;
}

float get_pitch(void)
{
    return pitch;
}

float get_yaw(void)
{
    float YAW = convert_to_continuous_yaw(yaw);
    return YAW;
}

// Global variables for continuous yaw conversion
float g_last_yaw = 0.0f;
int g_revolution_count = 0;
bool g_is_yaw_initialized = false;
/**
 * @brief Convert yaw from [-180, 180] range to continuous angle
 *
 * @param current_yaw Current yaw angle (-180 to 180 degrees)
 * @return float Continuous yaw angle (e.g., 370, -450 degrees)
 */
float convert_to_continuous_yaw(float current_yaw)
{
    // Threshold for detecting wrap-around (set to 300 degrees)
    const float WRAP_AROUND_THRESHOLD = 300.0f;

    // Initialize on first call
    if (!g_is_yaw_initialized)
    {
        g_last_yaw = current_yaw;
        g_is_yaw_initialized = true;
        g_revolution_count = 0;
    }

    // Calculate difference from last reading
    float diff = current_yaw - g_last_yaw;

    // Detect wrap-around conditions
    if (diff > WRAP_AROUND_THRESHOLD)
    {
        // Wrapped from positive to negative (e.g., 170� to -175�)
        // This indicates a backward revolution
        g_revolution_count--;
    }
    else if (diff < -WRAP_AROUND_THRESHOLD)
    {
        // Wrapped from negative to positive (e.g., -170� to 175�)
        // This indicates a forward revolution
        g_revolution_count++;
    }

    // Update last yaw for next comparison
    g_last_yaw = current_yaw;

    // Calculate continuous yaw
    float continuous_yaw = current_yaw + (float)g_revolution_count * 360.0f;

    return continuous_yaw;
}
