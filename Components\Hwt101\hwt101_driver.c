#include "hwt101_driver.h"
#include "stdio.h"
#include "string.h"

// 私有函数声明（保持原有）
static int8_t HWT101_ValidateParams(HWT101_t *hwt);
static uint8_t HWT101_CalculateChecksum(uint8_t *data, uint8_t length);
static int8_t HWT101_ParseGyroPacket(HWT101_t *hwt, uint8_t *packet);
static int8_t HWT101_ParseAnglePacket(HWT101_t *hwt, uint8_t *packet);
static float HWT101_ConvertGyroData(uint8_t low, uint8_t high);
static float HWT101_ConvertAngleData(uint8_t low, uint8_t high);
static int8_t HWT101_SendCommand(HWT101_t *hwt, uint8_t reg_addr, uint16_t data);
static int8_t HWT101_UnlockRegister(HWT101_t *hwt);


/**
 * @brief 创建HWT101实体
 * @note 初始化硬件配置和数据缓冲区，符合官方初始化流程
 */
HWT101_Error_t HWT101_Create(HWT101_t* hwt, UART_HandleTypeDef* huart, uint32_t timeout_ms)
{
    // 参数检查（增强为空指针和无效句柄判断）
    if (hwt == NULL || huart == NULL || huart->Instance == NULL) {
        return HWT101_ERR_PARAM;
    }

    // 超时时间默认值处理
    hwt->hw.timeout_ms = (timeout_ms == 0) ? HWT101_TIMEOUT_MS : timeout_ms;
    hwt->hw.huart = huart;

    // 初始化数据结构（清零更彻底）
    memset(&hwt->data, 0, sizeof(HWT101_Data_t));
    hwt->state = HWT101_STATE_IDLE;
    hwt->enable = 1;
    hwt->rx_index = 0;
    memset(hwt->rx_buffer, 0, HWT101_BUFFER_SIZE);

    return HWT101_OK;
}


/**
 * @brief 处理接收数据缓冲区
 * @note 优化状态机逻辑，增加超时重置机制，避免卡死
 */
HWT101_Error_t HWT101_ProcessBuffer(HWT101_t* hwt, uint8_t* buffer, uint16_t length)
{
    // 参数检查
    if (HWT101_ValidateParams(hwt) != 0 || buffer == NULL || length == 0) {
        return HWT101_ERR_PARAM;
    }
    if (!hwt->enable) {
        return HWT101_ERR_DISABLED;
    }

    // 记录当前时间，用于超时判断
    static uint32_t last_receive_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 处理每一个字节
    for (uint16_t i = 0; i < length; i++) {
        uint8_t byte = buffer[i];
        last_receive_time = current_time; // 更新接收时间

        switch (hwt->state) {
            case HWT101_STATE_IDLE:
                // 寻找帧头0x55
                if (byte == HWT101_HEADER) {
                    hwt->rx_buffer[0] = byte;
                    hwt->rx_index = 1;
                    hwt->state = HWT101_STATE_RECEIVING;
                }
                break;

            case HWT101_STATE_RECEIVING:
                // 接收数据，超过缓冲区大小则重置
                if (hwt->rx_index >= HWT101_BUFFER_SIZE) {
                    hwt->rx_index = 0;
                    hwt->state = HWT101_STATE_IDLE;
                    break;
                }
                hwt->rx_buffer[hwt->rx_index++] = byte;

                // 接收完整数据包（11字节）
                if (hwt->rx_index >= HWT101_PACKET_SIZE) {
                    // 校验和验证
                    uint8_t calc_sum = HWT101_CalculateChecksum(hwt->rx_buffer, HWT101_PACKET_SIZE - 1);
                    uint8_t recv_sum = hwt->rx_buffer[HWT101_PACKET_SIZE - 1];

                    if (calc_sum == recv_sum) {
                        // 解析数据包（严格判断类型）
                        uint8_t packet_type = hwt->rx_buffer[1];
                        if (packet_type == HWT101_TYPE_GYRO) {
                            HWT101_ParseGyroPacket(hwt, hwt->rx_buffer);
                        } else if (packet_type == HWT101_TYPE_ANGLE) {
                            HWT101_ParseAnglePacket(hwt, hwt->rx_buffer);
                        } else {
                            // 未知类型，标记为错误
                            hwt->state = HWT101_STATE_ERROR;
                        }
                        if (hwt->state != HWT101_STATE_ERROR) {
                            hwt->state = HWT101_STATE_DATA_READY;
                        }
                    } else {
                        hwt->state = HWT101_STATE_ERROR; // 校验和错误
                    }

                    // 重置接收状态
                    hwt->rx_index = 0;
                    if (hwt->state == HWT101_STATE_ERROR) {
                        hwt->state = HWT101_STATE_IDLE;
                    }
                }
                break;

            case HWT101_STATE_DATA_READY:
                // 处理完数据后立即切换到空闲态，准备接收下一包
                hwt->state = HWT101_STATE_IDLE;
                i--; // 重新处理当前字节
                break;

            case HWT101_STATE_ERROR:
                // 错误状态下直接寻找下一个帧头
                hwt->state = HWT101_STATE_IDLE;
                i--;
                break;

            default:
                hwt->state = HWT101_STATE_IDLE;
                break;
        }
    }

    // 超时判断：如果接收中超过100ms未收到新数据，重置状态
    if (hwt->state == HWT101_STATE_RECEIVING && 
        (current_time - last_receive_time) > 100) {
        hwt->rx_index = 0;
        hwt->state = HWT101_STATE_IDLE;
    }

    return HWT101_OK;
}


/**
 * @brief 保存配置
 * @note 改用HAL_UART_Transmit发送二进制命令，避免my_printf的格式问题
 */
HWT101_Error_t HWT101_SaveConfig(HWT101_t* hwt)
{
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable) {
        return HWT101_ERR_PARAM;
    }

    // 保存命令：FF AA 00 00 00（严格按照协议的二进制格式）
    uint8_t save_cmd[5] = {0xFF, 0xAA, HWT101_REG_SAVE, 0x00, 0x00};
    if (HAL_UART_Transmit(hwt->hw.huart, save_cmd, 5, hwt->hw.timeout_ms) != HAL_OK) {
        return HWT101_ERR_CMD_SEND; // 发送失败返回错误
    }

    HAL_Delay(100); // 确保配置生效
    return HWT101_OK;
}


/**
 * @brief 发送命令到HWT101
 * @note 核心优化：用HAL_UART_Transmit替代my_printf，确保二进制命令正确发送
 */
static int8_t HWT101_SendCommand(HWT101_t *hwt, uint8_t reg_addr, uint16_t data)
{
    if (HWT101_ValidateParams(hwt) != 0) {
        return -1;
    }

    // 命令格式：FF AA + 寄存器地址 + 数据低字节 + 数据高字节（共5字节）
    uint8_t cmd[5] = {
        HWT101_CMD_HEADER1,
        HWT101_CMD_HEADER2,
        reg_addr,
        (uint8_t)(data & 0xFF),       // 低字节
        (uint8_t)((data >> 8) & 0xFF) // 高字节
    };

    // 用HAL_UART_Transmit发送二进制数据（关键优化，避免my_printf的格式问题）
    if (HAL_UART_Transmit(hwt->hw.huart, cmd, 5, hwt->hw.timeout_ms) != HAL_OK) {
        return -1; // 发送超时或失败
    }

    HAL_Delay(100); // 等待传感器响应
    return 0;
}


/**
 * @brief 解锁寄存器
 * @note 优化发送方式，确保解锁命令正确送达
 */
static int8_t HWT101_UnlockRegister(HWT101_t *hwt)
{
    if (HWT101_ValidateParams(hwt) != 0) {
        return -1;
    }

    // 解锁序列：FF AA 69 88 B5（严格按照协议）
    uint8_t unlock_cmd[5] = {
        HWT101_CMD_HEADER1,
        HWT101_CMD_HEADER2,
        HWT101_UNLOCK_CODE1,
        HWT101_UNLOCK_CODE2,
        HWT101_UNLOCK_CODE3
    };

    // 用HAL_UART_Transmit发送，确保二进制数据正确
    if (HAL_UART_Transmit(hwt->hw.huart, unlock_cmd, 5, hwt->hw.timeout_ms) != HAL_OK) {
        return -1; // 解锁失败
    }

    HAL_Delay(100); // 必须等待解锁生效（官方要求）
    return 0;
}


/**
 * @brief Z轴角度归零（核心优化：确保命令发送和寄存器操作正确）
 */
HWT101_Error_t HWT101_ResetYaw(HWT101_t* hwt)
{
    // 参数检查
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable) {
        return HWT101_ERR_PARAM;
    }

    // 1. 解锁寄存器（必须步骤）
    if (HWT101_UnlockRegister(hwt) != 0) {
        return HWT101_ERR_UNLOCK; // 解锁失败
    }

    // 2. 发送归零命令（0x76寄存器，值0x0000）
    if (HWT101_SendCommand(hwt, HWT101_REG_CALIYAW, 0x0000) != 0) {
        return HWT101_ERR_CMD_SEND; // 命令发送失败
    }

    // 3. 保存配置（协议要求必须保存）
    if (HWT101_SaveConfig(hwt) != 0) {
        return HWT101_ERR_SAVE_CONFIG; // 保存失败
    }

    // 4. 等待归零完成（延长延时至500ms，确保传感器处理完成）
    HAL_Delay(500);

    return HWT101_OK; // 成功
}


/**
 * @brief 开始手动校准（优化命令发送可靠性）
 */
HWT101_Error_t HWT101_StartManualCalibration(HWT101_t* hwt)
{
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable) {
        return HWT101_ERR_PARAM;
    }

    // 解锁寄存器
    if (HWT101_UnlockRegister(hwt) != 0) {
        return HWT101_ERR_UNLOCK;
    }

    // 发送进入校准命令（0xA6寄存器，0x0001）
    if (HWT101_SendCommand(hwt, HWT101_REG_MANUALCALI, 0x0001) != 0) {
        return HWT101_ERR_CMD_SEND;
    }

    // 保存配置
    if (HWT101_SaveConfig(hwt) != 0) {
        return HWT101_ERR_SAVE_CONFIG;
    }

    HAL_Delay(500); // 等待命令生效
    return HWT101_OK;
}


/**
 * @brief 停止手动校准（同上优化）
 */
HWT101_Error_t HWT101_StopManualCalibration(HWT101_t* hwt)
{
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable) {
        return HWT101_ERR_PARAM;
    }

    if (HWT101_UnlockRegister(hwt) != 0) {
        return HWT101_ERR_UNLOCK;
    }

    if (HWT101_SendCommand(hwt, HWT101_REG_MANUALCALI, 0x0004) != 0) {
        return HWT101_ERR_CMD_SEND;
    }

    if (HWT101_SaveConfig(hwt) != 0) {
        return HWT101_ERR_SAVE_CONFIG;
    }

    HAL_Delay(500);
    return HWT101_OK;
}


/**
 * @brief 设置波特率（增加参数有效性判断）
 */
HWT101_Error_t HWT101_SetBaudRate(HWT101_t* hwt, uint8_t baud_code)
{
    // 波特率代码必须在1-7（对应4800-230400）
    if (baud_code < 1 || baud_code > 7) {
        return HWT101_ERR_PARAM;
    }
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable) {
        return HWT101_ERR_PARAM;
    }

    if (HWT101_UnlockRegister(hwt) != 0) {
        return HWT101_ERR_UNLOCK;
    }

    if (HWT101_SendCommand(hwt, HWT101_REG_BAUD, baud_code) != 0) {
        return HWT101_ERR_CMD_SEND;
    }

    if (HWT101_SaveConfig(hwt) != 0) {
        return HWT101_ERR_SAVE_CONFIG;
    }

    HAL_Delay(200); // 波特率修改需要更长生效时间
    return HWT101_OK;
}


/**
 * @brief 设置输出频率（优化同上）
 */
HWT101_Error_t HWT101_SetOutputRate(HWT101_t* hwt, uint8_t rate_code)
{
    // 频率代码范围：1-13，排除10（协议规定）
    if (rate_code < 1 || rate_code > 13 || rate_code == 10) {
        return HWT101_ERR_PARAM;
    }
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable) {
        return HWT101_ERR_PARAM;
    }

    if (HWT101_UnlockRegister(hwt) != 0) {
        return HWT101_ERR_UNLOCK;
    }

    if (HWT101_SendCommand(hwt, HWT101_REG_RRATE, rate_code) != 0) {
        return HWT101_ERR_CMD_SEND;
    }

    if (HWT101_SaveConfig(hwt) != 0) {
        return HWT101_ERR_SAVE_CONFIG;
    }

    HAL_Delay(200);
    return HWT101_OK;
}


/**
 * @brief 以下为原有函数，保持功能不变（仅优化细节）
 */

// 获取HWT101状态
HWT101_State_t HWT101_GetState(HWT101_t* hwt)
{
    return (HWT101_ValidateParams(hwt) != 0) ? HWT101_STATE_ERROR : hwt->state;
}

// 使能/失能HWT101
HWT101_Error_t HWT101_Enable(HWT101_t* hwt, uint8_t enable)
{
    if (HWT101_ValidateParams(hwt) != 0) {
        return HWT101_ERR_PARAM;
    }
    hwt->enable = enable;
    if (!enable) {
        memset(&hwt->data, 0, sizeof(HWT101_Data_t));
        hwt->state = HWT101_STATE_IDLE;
        hwt->rx_index = 0;
        memset(hwt->rx_buffer, 0, HWT101_BUFFER_SIZE);
    }
    return HWT101_OK;
}

// 获取角速度Z
float HWT101_GetGyroZ(HWT101_t* hwt)
{
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable || !hwt->data.data_valid) {
        return 0.0f;
    }
    return hwt->data.gyro_z;
}

// 获取偏航角
float HWT101_GetYaw(HWT101_t* hwt)
{
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable || !hwt->data.data_valid) {
        return 0.0f;
    }
    return hwt->data.yaw;
}

// 获取完整数据
HWT101_Data_t* HWT101_GetData(HWT101_t* hwt)
{
    if (HWT101_ValidateParams(hwt) != 0 || !hwt->enable || !hwt->data.data_valid) {
        return NULL;
    }
    return &hwt->data;
}

// 验证参数有效性
static int8_t HWT101_ValidateParams(HWT101_t *hwt)
{
    return (hwt == NULL || hwt->hw.huart == NULL || hwt->hw.huart->Instance == NULL) ? -1 : 0;
}

// 计算校验和
static uint8_t HWT101_CalculateChecksum(uint8_t *data, uint8_t length)
{
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

// 解析角速度数据包
static int8_t HWT101_ParseGyroPacket(HWT101_t *hwt, uint8_t *packet)
{
    // 严格按照协议格式解析：0x55 0x52 ... 
    if (packet[1] != HWT101_TYPE_GYRO) {
        return -1;
    }
    // 原始角速度Z（4-5字节）和校准后角速度Z（6-7字节）
    hwt->data.gyro_z_raw = HWT101_ConvertGyroData(packet[4], packet[5]);
    hwt->data.gyro_z = HWT101_ConvertGyroData(packet[6], packet[7]);
    hwt->data.timestamp = HAL_GetTick();
    hwt->data.data_valid = 1;
    return 0;
}

// 解析角度数据包
static int8_t HWT101_ParseAnglePacket(HWT101_t *hwt, uint8_t *packet)
{
    if (packet[1] != HWT101_TYPE_ANGLE) {
        return -1;
    }
    // 偏航角（6-7字节）和版本号（8-9字节）
    hwt->data.yaw = HWT101_ConvertAngleData(packet[6], packet[7]);
    hwt->data.version = (uint16_t)((packet[9] << 8) | packet[8]);
    hwt->data.timestamp = HAL_GetTick();
    hwt->data.data_valid = 1;
    return 0;
}

// 转换角速度数据
static float HWT101_ConvertGyroData(uint8_t low, uint8_t high)
{
    int16_t raw_data = (int16_t)((high << 8) | low); // 有符号转换
    return (float)raw_data / 32768.0f * 2000.0f; // 协议规定的转换公式
}

// 转换角度数据
static float HWT101_ConvertAngleData(uint8_t low, uint8_t high)
{
    int16_t raw_data = (int16_t)((high << 8) | low);
    return (float)raw_data / 32768.0f * 180.0f; // 协议规定的转换公式
}



