#include "scheduler.h"
#include "uart_app.h"  // 直接包含，避免循环依赖

// 任务结构体
typedef struct
{
	void (*task_func)(void); // 任务函数指针
	uint32_t rate_ms;		 // 执行周期（毫秒）
	uint32_t last_run;		 // 上次执行时间（初始化为 0，每次运行时刷新）
} scheduler_task_t;

// 全局变量，用于存储任务数量
uint8_t task_num;

/**
 * @brief 用户初始化函数
 * 非HAL库硬件初始化函数
 */
void System_Init()
{
	Led_Init();
	Uart_Init();
	save_initial_position();
	Step_Motor_Init();
	my_bno080_init();
	Emm_V5_Reset_CurPos_To_Zero(&huart4, 0x01);
  Emm_V5_Reset_CurPos_To_Zero(&huart2, 0x01);
	app_pid_init();
	init_vision_data();
	// 保存初始位置
	
	my_printf(&huart1, "sys ok\r\n");
	Emm_V5_Origin_Trigger_Return(&huart4,MOTOR_Y_ADDR,0,false); // Y轴回零点
	HAL_TIM_Base_Start_IT(&htim2);
}

// 静态任务数组，每个任务包含任务函数、执行周期（毫秒）和上次运行时间（毫秒）
static scheduler_task_t scheduler_task[] =
{
		{Step_Motor_Return,10,0}, // 回零点任务
//		{Led_Task, 1, 0},
//		{key_task, 10, 0},
		{Uart1_Task, 5, 0}, // 用户调试串口
//		{Uart5_Task, 5, 0},
		{bno080_task, 5, 0},
};

/**
 * @brief 调度器初始化函数
 * 计算任务数组的元素个数，并将结果存储在 task_num 中
 */
void Scheduler_Init(void)
{
	System_Init();
	task_num = sizeof(scheduler_task) / sizeof(scheduler_task_t);
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经超过任务的执行周期，则执行该任务并更新上次运行时间
 */
void Scheduler_Run(void)
{
	for (uint8_t i = 0; i < task_num; i++)
	{
		uint32_t now_time = HAL_GetTick();
		if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
		{
			scheduler_task[i].last_run = now_time;
			scheduler_task[i].task_func();
		}
	}
}

unsigned char measure_time3ms = 0; // 检测任务检测周期
unsigned char maixcam_time1ms = 0;
unsigned char key_time10ms = 0;
extern bool pid_running;

 void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	if(htim->Instance != htim2.Instance) return;

	// 检测任务
	if (++measure_time3ms >= 3)
	{
		measure_time3ms = 0;
	  app_pid_task();		// PID任务
	}
	
	if(++key_time10ms >= 10)
	{
		key_time10ms = 0;
		key_task();
	}
	
	if(++maixcam_time1ms >= 1)
	{
		maixcam_time1ms = 0;
		maixcam_task();
		Uart2_Task(); // X轴步进电机串口
		Uart4_Task(); // Y轴步进电机串口
	}
 }
