#ifndef __MYDEFINE_H__
#define __MYDEFINE_H__

/* ========== HAL 库头文件 ========== */
#include "main.h"
#include "gpio.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "i2c.h"

/* ========== C 语言头文件 ========== */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/* ========== 核心调度器头文件 ========== */
#include "Scheduler.h"

/* ========== 组件库头文件 ========== */
#include "pid.h"
#include "ringbuffer.h"

/* ========== 驱动库头文件 ========== */
#include "led_driver.h"
#include "Emm_V5.h"
#include "uart_driver.h"

/* ========== 应用层头文件========== */
#include "key_app.h"
#include "led_app.h"
#include "pid_app.h"
#include "servo_app.h"
//#include "task_app.h"
#include "maix_app.h"
// uart_app.h 移除，避免循环依赖，需要时在具体.c文件中包含

/* ========== 全局用户变量 ========== */
extern uint8_t led_buf[4];


#endif


