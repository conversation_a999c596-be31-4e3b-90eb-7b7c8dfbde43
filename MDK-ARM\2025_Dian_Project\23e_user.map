Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Init) for Scheduler_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Run) for Scheduler_Run
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_uart4_rx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart6_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to usart.o(.bss) for huart4
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to scheduler.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to scheduler.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    led_driver.o(i.Led_Display) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_driver.o(i.Led_Display) refers to led_driver.o(.data) for .data
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss) for .bss
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    uart_driver.o(i.Uart_Init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_driver.o(i.Uart_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(i.Uart_Init) refers to uart_driver.o(.bss) for .bss
    uart_driver.o(i.Uart_Init) refers to usart.o(.bss) for huart1
    uart_driver.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Subdivision) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Vel_Scale) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Parse_Response) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    bno08x_hal.o(i.BNO080_HardwareReset) refers to uart_driver.o(i.my_printf) for my_printf
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.BNO080_HardwareReset) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bno08x_hal.o(i.BNO080_HardwareReset) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.BNO080_HardwareReset) refers to usart.o(.bss) for huart1
    bno08x_hal.o(i.BNO080_HardwareReset) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.BNO080_Init) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to bno08x_hal.o(i.__ARM_isnanf) for __ARM_isnanf
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to bno08x_hal.o(i.__ARM_isinff) for __ARM_isinff
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    bno08x_hal.o(i.calibrateAccelerometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateAll) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateGyro) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateMagnetometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibratePlanarAccelerometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(i.parseInputReport) for parseInputReport
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.enableAccelerometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableGameRotationVector) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableGyro) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableLinearAccelerometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableMagnetometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableRotationVector) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableStabilityClassifier) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableStepCounter) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.endCalibration) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.frsReadRequest) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.frsReadRequest) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getAccelAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getAccelX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getAccelY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getAccelZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getActivityClassifier) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQ1) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ1) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getQ2) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ2) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getQ3) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ3) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getQuatAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatI) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatI) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatJ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatJ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatK) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatK) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatRadianAccuracy) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatRadianAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatReal) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatReal) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getRange) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getRange) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getResolution) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getResolution) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getStabilityClassifier) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getStepCount) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.parseInputReport) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.parseInputReport) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.qToFloat) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(i.frsReadRequest) for frsReadRequest
    bno08x_hal.o(i.readFRSdata) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.readFRSword) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.readFRSword) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.receivePacket) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    bno08x_hal.o(i.receivePacket) refers to uart_driver.o(i.my_printf) for my_printf
    bno08x_hal.o(i.receivePacket) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.receivePacket) refers to usart.o(.bss) for huart5
    bno08x_hal.o(i.receivePacket) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.saveCalibration) refers to bno08x_hal.o(i.sendCommand) for sendCommand
    bno08x_hal.o(i.saveCalibration) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.sendCalibrateCommand) refers to bno08x_hal.o(i.sendCommand) for sendCommand
    bno08x_hal.o(i.sendCalibrateCommand) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.sendPacket) refers to malloc.o(i.malloc) for malloc
    bno08x_hal.o(i.sendPacket) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    bno08x_hal.o(i.sendPacket) refers to malloc.o(i.free) for free
    bno08x_hal.o(i.sendPacket) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.sendPacket) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.setFeatureCommand) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.setFeatureCommand) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.softReset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(.data) refers to bno08x_hal.o(.bss) for activityConfidences
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to bno08x_app.o(i.bno080_task) for bno080_task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to uart_app.o(i.Uart5_Task) for Uart5_Task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to feedforward_app.o(i.feedforward_task) for feedforward_task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_app.o(i.app_pid_task) for app_pid_task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to key_app.o(i.key_task) for key_task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to maix_app.o(i.maixcam_task) for maixcam_task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim2
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to scheduler.o(.data) for .data
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(i.System_Init) for System_Init
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(.data) for .data
    scheduler.o(i.Scheduler_Run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.Scheduler_Run) refers to scheduler.o(.data) for .data
    scheduler.o(i.System_Init) refers to led_app.o(i.Led_Init) for Led_Init
    scheduler.o(i.System_Init) refers to uart_driver.o(i.Uart_Init) for Uart_Init
    scheduler.o(i.System_Init) refers to uart_app.o(i.save_initial_position) for save_initial_position
    scheduler.o(i.System_Init) refers to servo_app.o(i.Step_Motor_Init) for Step_Motor_Init
    scheduler.o(i.System_Init) refers to bno08x_app.o(i.my_bno080_init) for my_bno080_init
    scheduler.o(i.System_Init) refers to emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    scheduler.o(i.System_Init) refers to pid_app.o(i.app_pid_init) for app_pid_init
    scheduler.o(i.System_Init) refers to feedforward_app.o(i.feedforward_init) for feedforward_init
    scheduler.o(i.System_Init) refers to maix_app.o(i.init_vision_data) for init_vision_data
    scheduler.o(i.System_Init) refers to uart_driver.o(i.my_printf) for my_printf
    scheduler.o(i.System_Init) refers to emm_v5.o(i.Emm_V5_Origin_Trigger_Return) for Emm_V5_Origin_Trigger_Return
    scheduler.o(i.System_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    scheduler.o(i.System_Init) refers to usart.o(.bss) for huart4
    scheduler.o(i.System_Init) refers to tim.o(.bss) for htim2
    scheduler.o(.data) refers to servo_app.o(i.Step_Motor_Return) for Step_Motor_Return
    scheduler.o(.data) refers to uart_app.o(i.Uart1_Task) for Uart1_Task
    led_app.o(i.Led_Task) refers to led_driver.o(i.Led_Display) for Led_Display
    led_app.o(i.Led_Task) refers to led_app.o(.data) for .data
    key_app.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.key_task) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_task) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    key_app.o(i.key_task) refers to pid_app.o(i.app_pid_start) for app_pid_start
    key_app.o(i.key_task) refers to pid_app.o(i.app_pid_stop) for app_pid_stop
    key_app.o(i.key_task) refers to servo_app.o(i.Step_Motor_Stop) for Step_Motor_Stop
    key_app.o(i.key_task) refers to key_app.o(.data) for .data
    pid_app.o(i.app_pid_calc) refers to pid_app.o(i.pid_follow) for pid_follow
    pid_app.o(i.app_pid_calc) refers to pid_app.o(i.pid_ju) for pid_ju
    pid_app.o(i.app_pid_calc) refers to pid_app.o(i.pid_circle) for pid_circle
    pid_app.o(i.app_pid_calc) refers to pid_app.o(i.pid_only_target) for pid_only_target
    pid_app.o(i.app_pid_calc) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.app_pid_calc) refers to servo_app.o(i.Step_Motor_Stop) for Step_Motor_Stop
    pid_app.o(i.app_pid_calc) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.app_pid_calc) refers to pid.o(i.pid_app_limit_integral) for pid_app_limit_integral
    pid_app.o(i.app_pid_calc) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.app_pid_calc) refers to feedforward_app.o(i.feedforward_get_compensation) for feedforward_get_compensation
    pid_app.o(i.app_pid_calc) refers to servo_app.o(i.Step_Motor_Set_Speed_my) for Step_Motor_Set_Speed_my
    pid_app.o(i.app_pid_calc) refers to uart_app.o(.data) for pid_mode
    pid_app.o(i.app_pid_calc) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_calc) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.app_pid_calc) refers to usart.o(.bss) for huart1
    pid_app.o(i.app_pid_init) refers to pid.o(i.pid_init) for pid_init
    pid_app.o(i.app_pid_init) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_init) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.app_pid_parse_cmd) refers to _scanf_int.o(.text) for _scanf_int
    pid_app.o(i.app_pid_parse_cmd) refers to __0sscanf.o(.text) for __0sscanf
    pid_app.o(i.app_pid_parse_cmd) refers to strncmp.o(.text) for strncmp
    pid_app.o(i.app_pid_parse_cmd) refers to feedforward_app.o(i.feedforward_print_status) for feedforward_print_status
    pid_app.o(i.app_pid_parse_cmd) refers to feedforward_app.o(i.feedforward_print_compensation) for feedforward_print_compensation
    pid_app.o(i.app_pid_parse_cmd) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    pid_app.o(i.app_pid_parse_cmd) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.app_pid_parse_cmd) refers to memcpya.o(.text) for __aeabi_memcpy4
    pid_app.o(i.app_pid_parse_cmd) refers to pid_app.o(i.app_pid_set_x_params) for app_pid_set_x_params
    pid_app.o(i.app_pid_parse_cmd) refers to f2d.o(.text) for __aeabi_f2d
    pid_app.o(i.app_pid_parse_cmd) refers to pid_app.o(i.app_pid_set_y_params) for app_pid_set_y_params
    pid_app.o(i.app_pid_parse_cmd) refers to pid_app.o(i.app_pid_start) for app_pid_start
    pid_app.o(i.app_pid_parse_cmd) refers to pid_app.o(i.app_pid_stop) for app_pid_stop
    pid_app.o(i.app_pid_parse_cmd) refers to pid_app.o(i.app_pid_init) for app_pid_init
    pid_app.o(i.app_pid_parse_cmd) refers to feedforward_app.o(i.feedforward_set_parameters) for feedforward_set_parameters
    pid_app.o(i.app_pid_parse_cmd) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_parse_cmd) refers to usart.o(.bss) for huart1
    pid_app.o(i.app_pid_parse_cmd) refers to uart_app.o(.data) for pid_mode
    pid_app.o(i.app_pid_parse_cmd) refers to feedforward_app.o(.bss) for ff_controller
    pid_app.o(i.app_pid_set_target) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.app_pid_set_target) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_set_target) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.app_pid_set_x_params) refers to memcpya.o(.text) for __aeabi_memcpy4
    pid_app.o(i.app_pid_set_x_params) refers to pid.o(i.pid_set_params) for pid_set_params
    pid_app.o(i.app_pid_set_x_params) refers to pid.o(i.pid_set_limit) for pid_set_limit
    pid_app.o(i.app_pid_set_x_params) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_set_x_params) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.app_pid_set_y_params) refers to memcpya.o(.text) for __aeabi_memcpy4
    pid_app.o(i.app_pid_set_y_params) refers to pid.o(i.pid_set_params) for pid_set_params
    pid_app.o(i.app_pid_set_y_params) refers to pid.o(i.pid_set_limit) for pid_set_limit
    pid_app.o(i.app_pid_set_y_params) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_set_y_params) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.app_pid_start) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.app_pid_start) refers to pid.o(i.pid_reset) for pid_reset
    pid_app.o(i.app_pid_start) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_start) refers to usart.o(.bss) for huart1
    pid_app.o(i.app_pid_start) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.app_pid_stop) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.app_pid_stop) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_stop) refers to usart.o(.bss) for huart1
    pid_app.o(i.app_pid_task) refers to pid_app.o(i.app_pid_calc) for app_pid_calc
    pid_app.o(i.app_pid_task) refers to pid_app.o(.data) for .data
    pid_app.o(i.app_pid_update_position) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.app_pid_update_position) refers to pid_app.o(.data) for .data
    pid_app.o(i.pid_circle) refers to f2d.o(.text) for __aeabi_f2d
    pid_app.o(i.pid_circle) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.pid_circle) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    pid_app.o(i.pid_circle) refers to memseta.o(.text) for __aeabi_memclr4
    pid_app.o(i.pid_circle) refers to servo_app.o(i.Step_Motor_Stop) for Step_Motor_Stop
    pid_app.o(i.pid_circle) refers to pid_app.o(i.app_pid_stop) for app_pid_stop
    pid_app.o(i.pid_circle) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.pid_circle) refers to pid_app.o(.data) for .data
    pid_app.o(i.pid_circle) refers to usart.o(.bss) for huart1
    pid_app.o(i.pid_circle) refers to task_app.o(.bss) for Circle_pidTargetPoints
    pid_app.o(i.pid_follow) refers to f2d.o(.text) for __aeabi_f2d
    pid_app.o(i.pid_follow) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.pid_follow) refers to task_app.o(.data) for latest_blue_laser_coord
    pid_app.o(i.pid_follow) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.pid_follow) refers to usart.o(.bss) for huart1
    pid_app.o(i.pid_ju) refers to f2d.o(.text) for __aeabi_f2d
    pid_app.o(i.pid_ju) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.pid_ju) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    pid_app.o(i.pid_ju) refers to memseta.o(.text) for __aeabi_memclr4
    pid_app.o(i.pid_ju) refers to servo_app.o(i.Step_Motor_Stop) for Step_Motor_Stop
    pid_app.o(i.pid_ju) refers to pid_app.o(i.app_pid_stop) for app_pid_stop
    pid_app.o(i.pid_ju) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.pid_ju) refers to pid_app.o(.data) for .data
    pid_app.o(i.pid_ju) refers to usart.o(.bss) for huart1
    pid_app.o(i.pid_ju) refers to uart_app.o(.bss) for g_pidTargetPoints
    pid_app.o(i.pid_only_target) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    pid_app.o(i.pid_only_target) refers to f2d.o(.text) for __aeabi_f2d
    pid_app.o(i.pid_only_target) refers to uart_driver.o(i.my_printf) for my_printf
    pid_app.o(i.pid_only_target) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.pid_only_target) refers to usart.o(.bss) for huart1
    uart_app.o(i.Uart1_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.Uart1_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.Uart1_Task) refers to uart_app.o(i.process_command) for process_command
    uart_app.o(i.Uart1_Task) refers to pid_app.o(i.app_pid_parse_cmd) for app_pid_parse_cmd
    uart_app.o(i.Uart1_Task) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.Uart1_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.Uart1_Task) refers to uart_driver.o(.bss) for ring1_buffer
    uart_app.o(i.Uart1_Task) refers to uart_driver.o(.bss) for uart1_data_buffer
    uart_app.o(i.Uart1_Task) refers to usart.o(.bss) for huart1
    uart_app.o(i.Uart2_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.Uart2_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.Uart2_Task) refers to emm_v5.o(i.Emm_V5_Parse_Response) for Emm_V5_Parse_Response
    uart_app.o(i.Uart2_Task) refers to uart_app.o(i.parse_x_motor_data) for parse_x_motor_data
    uart_app.o(i.Uart2_Task) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.Uart2_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.Uart2_Task) refers to uart_app.o(i.check_motor_angle_limits) for check_motor_angle_limits
    uart_app.o(i.Uart2_Task) refers to uart_driver.o(.bss) for ring2_buffer
    uart_app.o(i.Uart2_Task) refers to uart_driver.o(.bss) for uart2_data_buffer
    uart_app.o(i.Uart2_Task) refers to usart.o(.bss) for huart1
    uart_app.o(i.Uart4_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.Uart4_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.Uart4_Task) refers to emm_v5.o(i.Emm_V5_Parse_Response) for Emm_V5_Parse_Response
    uart_app.o(i.Uart4_Task) refers to uart_app.o(i.parse_y_motor_data) for parse_y_motor_data
    uart_app.o(i.Uart4_Task) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.Uart4_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.Uart4_Task) refers to uart_app.o(i.check_motor_angle_limits) for check_motor_angle_limits
    uart_app.o(i.Uart4_Task) refers to uart_driver.o(.bss) for ring4_buffer
    uart_app.o(i.Uart4_Task) refers to uart_driver.o(.bss) for uart4_data_buffer
    uart_app.o(i.Uart4_Task) refers to usart.o(.bss) for huart1
    uart_app.o(i.Uart5_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.Uart5_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.Uart5_Task) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.Uart5_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.Uart5_Task) refers to uart_driver.o(.bss) for ring5_buffer
    uart_app.o(i.Uart5_Task) refers to uart_driver.o(.bss) for uart5_data_buffer
    uart_app.o(i.Uart5_Task) refers to usart.o(.bss) for huart5
    uart_app.o(i.calculate_and_store_perimeter_points) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.calculate_and_store_perimeter_points) refers to dflti.o(.text) for __aeabi_i2d
    uart_app.o(i.calculate_and_store_perimeter_points) refers to ddiv.o(.text) for __aeabi_ddiv
    uart_app.o(i.calculate_and_store_perimeter_points) refers to dmul.o(.text) for __aeabi_dmul
    uart_app.o(i.calculate_and_store_perimeter_points) refers to dadd.o(.text) for __aeabi_dadd
    uart_app.o(i.calculate_and_store_perimeter_points) refers to round.o(i.__hardfp_round) for __hardfp_round
    uart_app.o(i.calculate_and_store_perimeter_points) refers to dfixi.o(.text) for __aeabi_d2iz
    uart_app.o(i.calculate_and_store_perimeter_points) refers to pid.o(i.pid_set_target) for pid_set_target
    uart_app.o(i.calculate_and_store_perimeter_points) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.calculate_and_store_perimeter_points) refers to usart.o(.bss) for huart1
    uart_app.o(i.calculate_and_store_perimeter_points) refers to pid_app.o(.bss) for pid_x
    uart_app.o(i.check_motor_angle_limits) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.check_motor_angle_limits) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    uart_app.o(i.check_motor_angle_limits) refers to uart_app.o(.data) for .data
    uart_app.o(i.check_motor_angle_limits) refers to usart.o(.bss) for huart1
    uart_app.o(i.parse_x_motor_data) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.parse_x_motor_data) refers to uart_app.o(i.calc_motor_angle) for calc_motor_angle
    uart_app.o(i.parse_x_motor_data) refers to uart_app.o(i.calc_relative_angle) for calc_relative_angle
    uart_app.o(i.parse_x_motor_data) refers to f2d.o(.text) for __aeabi_f2d
    uart_app.o(i.parse_x_motor_data) refers to usart.o(.bss) for huart1
    uart_app.o(i.parse_x_motor_data) refers to uart_app.o(.data) for .data
    uart_app.o(i.parse_x_motor_data) refers to uart_app.o(.conststring) for .conststring
    uart_app.o(i.parse_y_motor_data) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.parse_y_motor_data) refers to uart_app.o(i.calc_motor_angle) for calc_motor_angle
    uart_app.o(i.parse_y_motor_data) refers to uart_app.o(i.calc_relative_angle) for calc_relative_angle
    uart_app.o(i.parse_y_motor_data) refers to f2d.o(.text) for __aeabi_f2d
    uart_app.o(i.parse_y_motor_data) refers to usart.o(.bss) for huart1
    uart_app.o(i.parse_y_motor_data) refers to uart_app.o(.data) for .data
    uart_app.o(i.parse_y_motor_data) refers to uart_app.o(.conststring) for .conststring
    uart_app.o(i.process_command) refers to _scanf_int.o(.text) for _scanf_int
    uart_app.o(i.process_command) refers to strncmp.o(.text) for strncmp
    uart_app.o(i.process_command) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.process_command) refers to uart_app.o(i.process_reset_command) for process_reset_command
    uart_app.o(i.process_command) refers to emm_v5.o(i.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    uart_app.o(i.process_command) refers to __0sscanf.o(.text) for __0sscanf
    uart_app.o(i.process_command) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    uart_app.o(i.process_command) refers to task_app.o(i.generateCirclePoints) for generateCirclePoints
    uart_app.o(i.process_command) refers to memcpya.o(.text) for __aeabi_memcpy4
    uart_app.o(i.process_command) refers to uart_app.o(i.calculate_and_store_perimeter_points) for calculate_and_store_perimeter_points
    uart_app.o(i.process_command) refers to uart_app.o(.data) for .data
    uart_app.o(i.process_command) refers to usart.o(.bss) for huart1
    uart_app.o(i.process_command) refers to task_app.o(.data) for latest_blue_laser_coord
    uart_app.o(i.process_command) refers to task_app.o(.bss) for Circle_pidTargetPoints
    uart_app.o(i.process_command) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.process_command) refers to uart_app.o(.conststring) for .conststring
    uart_app.o(i.process_reset_command) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.process_reset_command) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    uart_app.o(i.process_reset_command) refers to uart_app.o(.data) for .data
    uart_app.o(i.process_reset_command) refers to usart.o(.bss) for huart1
    uart_app.o(i.save_initial_position) refers to uart_driver.o(i.my_printf) for my_printf
    uart_app.o(i.save_initial_position) refers to emm_v5.o(i.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    uart_app.o(i.save_initial_position) refers to uart_app.o(.data) for .data
    uart_app.o(i.save_initial_position) refers to usart.o(.bss) for huart1
    servo_app.o(i.Step_Motor_Init) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    servo_app.o(i.Step_Motor_Init) refers to emm_v5.o(i.Emm_V5_Modify_Vel_Scale) for Emm_V5_Modify_Vel_Scale
    servo_app.o(i.Step_Motor_Init) refers to servo_app.o(i.Step_Motor_Stop) for Step_Motor_Stop
    servo_app.o(i.Step_Motor_Init) refers to usart.o(.bss) for huart2
    servo_app.o(i.Step_Motor_Return) refers to emm_v5.o(i.Emm_V5_Origin_Trigger_Return) for Emm_V5_Origin_Trigger_Return
    servo_app.o(i.Step_Motor_Return) refers to servo_app.o(.data) for .data
    servo_app.o(i.Step_Motor_Return) refers to usart.o(.bss) for huart4
    servo_app.o(i.Step_Motor_Set_Dis) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    servo_app.o(i.Step_Motor_Set_Dis) refers to usart.o(.bss) for huart2
    servo_app.o(i.Step_Motor_Set_Speed) refers to uart_driver.o(i.my_printf) for my_printf
    servo_app.o(i.Step_Motor_Set_Speed) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    servo_app.o(i.Step_Motor_Set_Speed) refers to usart.o(.bss) for huart1
    servo_app.o(i.Step_Motor_Set_Speed_my) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    servo_app.o(i.Step_Motor_Set_Speed_my) refers to usart.o(.bss) for huart2
    servo_app.o(i.Step_Motor_Stop) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    servo_app.o(i.Step_Motor_Stop) refers to usart.o(.bss) for huart2
    task_app.o(i.generateCirclePoints) refers to dflti.o(.text) for __aeabi_i2d
    task_app.o(i.generateCirclePoints) refers to dmul.o(.text) for __aeabi_dmul
    task_app.o(i.generateCirclePoints) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    task_app.o(i.generateCirclePoints) refers to dadd.o(.text) for __aeabi_dadd
    task_app.o(i.generateCirclePoints) refers to dfixi.o(.text) for __aeabi_d2iz
    task_app.o(i.generateCirclePoints) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    task_app.o(i.generateCirclePoints) refers to memcpya.o(.text) for __aeabi_memcpy4
    task_app.o(i.generate_perimeter_points) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    task_app.o(i.generate_perimeter_points) refers to uart_app.o(.data) for pid_mode
    task_app.o(i.pi_parse_data) refers to _scanf_int.o(.text) for _scanf_int
    task_app.o(i.pi_parse_data) refers to _scanf_str.o(.text) for _scanf_string
    task_app.o(i.pi_parse_data) refers to strrchr.o(.text) for strrchr
    task_app.o(i.pi_parse_data) refers to __0sscanf.o(.text) for __0sscanf
    task_app.o(i.pi_parse_data) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    task_app.o(i.pi_parse_data) refers to pid_app.o(i.app_pid_update_position) for app_pid_update_position
    task_app.o(i.pi_parse_data) refers to strchr.o(.text) for strchr
    task_app.o(i.pi_parse_data) refers to uart_driver.o(i.my_printf) for my_printf
    task_app.o(i.pi_parse_data) refers to task_app.o(i.sort_rect_clockwise) for sort_rect_clockwise
    task_app.o(i.pi_parse_data) refers to task_app.o(i.generate_perimeter_points) for generate_perimeter_points
    task_app.o(i.pi_parse_data) refers to task_app.o(.data) for .data
    task_app.o(i.pi_parse_data) refers to usart.o(.bss) for huart1
    task_app.o(i.pi_parse_data) refers to uart_app.o(.bss) for g_pidTargetPoints
    task_app.o(i.sort_rect_clockwise) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    maix_app.o(i.MAIX_ParseCamData) refers to malloc.o(i.malloc) for malloc
    maix_app.o(i.MAIX_ParseCamData) refers to malloc.o(i.free) for free
    maix_app.o(i.MAIX_ParseCamData) refers to memcpya.o(.text) for __aeabi_memcpy
    maix_app.o(i.MAIX_ParseCamData) refers to strtok_r.o(.text) for strtok_r
    maix_app.o(i.MAIX_ParseCamData) refers to atoi.o(.text) for atoi
    maix_app.o(i.MAIX_ParseCamData) refers to maix_app.o(.bss) for .bss
    maix_app.o(i.handle_maix_camera_data_parsing) refers to maix_app.o(i.MAIX_ParseCamData) for MAIX_ParseCamData
    maix_app.o(i.handle_maix_camera_data_parsing) refers to maix_app.o(i.calculate_circle_center_from_trajectory) for calculate_circle_center_from_trajectory
    maix_app.o(i.handle_maix_camera_data_parsing) refers to uart_driver.o(i.my_printf) for my_printf
    maix_app.o(i.handle_maix_camera_data_parsing) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    maix_app.o(i.handle_maix_camera_data_parsing) refers to pid_app.o(i.app_pid_update_position) for app_pid_update_position
    maix_app.o(i.handle_maix_camera_data_parsing) refers to maix_app.o(.bss) for .bss
    maix_app.o(i.handle_maix_camera_data_parsing) refers to maix_app.o(.data) for .data
    maix_app.o(i.handle_maix_camera_data_parsing) refers to usart.o(.bss) for huart1
    maix_app.o(i.handle_maix_camera_data_parsing) refers to uart_app.o(.data) for pid_mode
    maix_app.o(i.init_vision_data) refers to maix_app.o(.bss) for .bss
    maix_app.o(i.maixcam_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    maix_app.o(i.maixcam_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    maix_app.o(i.maixcam_task) refers to maix_app.o(i.parse_uart_message) for parse_uart_message
    maix_app.o(i.maixcam_task) refers to pid_app.o(i.app_pid_update_position) for app_pid_update_position
    maix_app.o(i.maixcam_task) refers to servo_app.o(i.Step_Motor_Stop) for Step_Motor_Stop
    maix_app.o(i.maixcam_task) refers to pid_app.o(i.app_pid_set_target) for app_pid_set_target
    maix_app.o(i.maixcam_task) refers to memseta.o(.text) for __aeabi_memclr
    maix_app.o(i.maixcam_task) refers to uart_driver.o(.bss) for ring6_buffer
    maix_app.o(i.maixcam_task) refers to uart_driver.o(.bss) for uart6_data_buffer
    maix_app.o(i.maixcam_task) refers to maix_app.o(.bss) for .bss
    maix_app.o(i.maixcam_task) refers to uart_app.o(.data) for pid_mode
    maix_app.o(i.maixcam_task) refers to pid_app.o(.data) for pid_running
    maix_app.o(i.maixcam_task) refers to key_app.o(.data) for turn_flag
    maix_app.o(i.parse_uart_message) refers to _scanf_int.o(.text) for _scanf_int
    maix_app.o(i.parse_uart_message) refers to strncmp.o(.text) for strncmp
    maix_app.o(i.parse_uart_message) refers to __0sscanf.o(.text) for __0sscanf
    maix_app.o(i.parse_uart_message) refers to maix_app.o(.bss) for .bss
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.dataAvailable) for dataAvailable
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatI) for getQuatI
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatJ) for getQuatJ
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatK) for getQuatK
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatReal) for getQuatReal
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.QuaternionToEulerAngles) for QuaternionToEulerAngles
    bno08x_app.o(i.bno080_task) refers to f2d.o(.text) for __aeabi_f2d
    bno08x_app.o(i.bno080_task) refers to uart_driver.o(i.my_printf) for my_printf
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.enableRotationVector) for enableRotationVector
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.enableAccelerometer) for enableAccelerometer
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.enableGyro) for enableGyro
    bno08x_app.o(i.bno080_task) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.bno080_task) refers to usart.o(.bss) for huart5
    bno08x_app.o(i.convert_to_continuous_yaw) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.get_pitch) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.get_roll) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.get_yaw) refers to bno08x_app.o(i.convert_to_continuous_yaw) for convert_to_continuous_yaw
    bno08x_app.o(i.get_yaw) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.my_bno080_init) refers to uart_driver.o(i.my_printf) for my_printf
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.BNO080_Init) for BNO080_Init
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.BNO080_HardwareReset) for BNO080_HardwareReset
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.softReset) for softReset
    bno08x_app.o(i.my_bno080_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.enableRotationVector) for enableRotationVector
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.enableAccelerometer) for enableAccelerometer
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.enableGyro) for enableGyro
    bno08x_app.o(i.my_bno080_init) refers to usart.o(.bss) for huart5
    bno08x_app.o(i.my_bno080_init) refers to i2c.o(.bss) for hi2c1
    feedforward_app.o(i.feedforward_calculate_compensation) refers to pid.o(i.pid_constrain) for pid_constrain
    feedforward_app.o(i.feedforward_calculate_compensation) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_calibrate) refers to uart_driver.o(i.my_printf) for my_printf
    feedforward_app.o(i.feedforward_calibrate) refers to feedforward_app.o(i.feedforward_update_imu_data) for feedforward_update_imu_data
    feedforward_app.o(i.feedforward_calibrate) refers to f2d.o(.text) for __aeabi_f2d
    feedforward_app.o(i.feedforward_calibrate) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_calibrate) refers to usart.o(.bss) for huart1
    feedforward_app.o(i.feedforward_get_compensation) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_identify_motion_state) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_init) refers to uart_driver.o(i.my_printf) for my_printf
    feedforward_app.o(i.feedforward_init) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_init) refers to usart.o(.bss) for huart1
    feedforward_app.o(i.feedforward_print_compensation) refers to f2d.o(.text) for __aeabi_f2d
    feedforward_app.o(i.feedforward_print_compensation) refers to uart_driver.o(i.my_printf) for my_printf
    feedforward_app.o(i.feedforward_print_compensation) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_print_compensation) refers to usart.o(.bss) for huart1
    feedforward_app.o(i.feedforward_print_status) refers to uart_driver.o(i.my_printf) for my_printf
    feedforward_app.o(i.feedforward_print_status) refers to feedforward_app.o(.constdata) for .constdata
    feedforward_app.o(i.feedforward_print_status) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_print_status) refers to usart.o(.bss) for huart1
    feedforward_app.o(i.feedforward_process) refers to feedforward_app.o(i.feedforward_update_imu_data) for feedforward_update_imu_data
    feedforward_app.o(i.feedforward_process) refers to feedforward_app.o(i.feedforward_identify_motion_state) for feedforward_identify_motion_state
    feedforward_app.o(i.feedforward_process) refers to feedforward_app.o(i.feedforward_calibrate) for feedforward_calibrate
    feedforward_app.o(i.feedforward_process) refers to f2d.o(.text) for __aeabi_f2d
    feedforward_app.o(i.feedforward_process) refers to uart_driver.o(i.my_printf) for my_printf
    feedforward_app.o(i.feedforward_process) refers to feedforward_app.o(i.feedforward_calculate_compensation) for feedforward_calculate_compensation
    feedforward_app.o(i.feedforward_process) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_process) refers to feedforward_app.o(.data) for .data
    feedforward_app.o(i.feedforward_process) refers to usart.o(.bss) for huart5
    feedforward_app.o(i.feedforward_set_parameters) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_set_physical_params) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(i.feedforward_task) refers to feedforward_app.o(i.feedforward_process) for feedforward_process
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_hal.o(i.dataAvailable) for dataAvailable
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_hal.o(i.getAccelX) for getAccelX
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_hal.o(i.getAccelY) for getAccelY
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_hal.o(i.getAccelZ) for getAccelZ
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_hal.o(i.getGyroX) for getGyroX
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_hal.o(i.getGyroY) for getGyroY
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_hal.o(i.getGyroZ) for getGyroZ
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_app.o(i.get_roll) for get_roll
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_app.o(i.get_pitch) for get_pitch
    feedforward_app.o(i.feedforward_update_imu_data) refers to bno08x_app.o(i.get_yaw) for get_yaw
    feedforward_app.o(i.feedforward_update_imu_data) refers to feedforward_app.o(.bss) for .bss
    feedforward_app.o(.constdata) refers to feedforward_app.o(.conststring) for .conststring
    asinf.o(i.__hardfp_asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.__hardfp_asinf) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf.o(i.__hardfp_asinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf.o(i.__hardfp_asinf) refers to errno.o(i.__set_errno) for __set_errno
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    asinf.o(i.__softfp_asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.__softfp_asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf.o(i.asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf_x.o(i.____hardfp_asinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf_x.o(i.____hardfp_asinf$lsc) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf_x.o(i.____hardfp_asinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf_x.o(i.____hardfp_asinf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asinf_x.o(i.____softfp_asinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf_x.o(i.____softfp_asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    asinf_x.o(i.__asinf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf_x.o(i.__asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    atan2f.o(i.__hardfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    cos.o(i.__hardfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    powf.o(i.__hardfp_powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__hardfp_powf) refers to fpstat.o(.text) for __ieee_status
    powf.o(i.__hardfp_powf) refers to errno.o(i.__set_errno) for __set_errno
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.__hardfp_powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.__hardfp_powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(i.__softfp_powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(i.powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers to fpstat.o(.text) for __ieee_status
    powf_x.o(i.____hardfp_powf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    powf_x.o(i.____hardfp_powf$lsc) refers to powf_x.o(.constdata) for .constdata
    powf_x.o(i.____hardfp_powf$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf_x.o(i.____softfp_powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____softfp_powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(i.__powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.__powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.__hardfp_round) refers to drnd.o(.text) for _drnd
    round.o(i.__hardfp_round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.__hardfp_round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.__hardfp_round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    round.o(i.round) refers (Special) to iusefp.o(.text) for __I$use$fp
    round.o(i.round) refers to drnd.o(.text) for _drnd
    round.o(i.round) refers to dadd.o(.text) for __aeabi_dsub
    round.o(i.round) refers to cdcmple.o(.text) for __aeabi_cdcmple
    round.o(i.round) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    sin.o(i.__hardfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to errno.o(i.__set_errno) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f407xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    drnd.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    drnd.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (52 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (232 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing led_driver.o(.rev16_text), (4 bytes).
    Removing led_driver.o(.revsh_text), (4 bytes).
    Removing led_driver.o(.rrx_text), (6 bytes).
    Removing led_driver.o(i.Led_Display), (140 bytes).
    Removing led_driver.o(.data), (1 bytes).
    Removing pid.o(.rev16_text), (4 bytes).
    Removing pid.o(.revsh_text), (4 bytes).
    Removing pid.o(.rrx_text), (6 bytes).
    Removing pid.o(i.pid_calculate_incremental), (122 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (92 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (8 bytes).
    Removing uart_driver.o(.rev16_text), (4 bytes).
    Removing uart_driver.o(.revsh_text), (4 bytes).
    Removing uart_driver.o(.rrx_text), (6 bytes).
    Removing uart_driver.o(.bss), (128 bytes).
    Removing uart_driver.o(.bss), (128 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Subdivision), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (158 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Parse_Response), (492 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (48 bytes).
    Removing bno08x_hal.o(.rev16_text), (4 bytes).
    Removing bno08x_hal.o(.revsh_text), (4 bytes).
    Removing bno08x_hal.o(.rrx_text), (6 bytes).
    Removing bno08x_hal.o(i.calibrateAccelerometer), (6 bytes).
    Removing bno08x_hal.o(i.calibrateAll), (6 bytes).
    Removing bno08x_hal.o(i.calibrateGyro), (6 bytes).
    Removing bno08x_hal.o(i.calibrateMagnetometer), (6 bytes).
    Removing bno08x_hal.o(i.calibratePlanarAccelerometer), (6 bytes).
    Removing bno08x_hal.o(i.enableGameRotationVector), (10 bytes).
    Removing bno08x_hal.o(i.enableLinearAccelerometer), (10 bytes).
    Removing bno08x_hal.o(i.enableMagnetometer), (10 bytes).
    Removing bno08x_hal.o(i.enableStabilityClassifier), (10 bytes).
    Removing bno08x_hal.o(i.enableStepCounter), (10 bytes).
    Removing bno08x_hal.o(i.endCalibration), (6 bytes).
    Removing bno08x_hal.o(i.frsReadRequest), (44 bytes).
    Removing bno08x_hal.o(i.getAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getActivityClassifier), (12 bytes).
    Removing bno08x_hal.o(i.getGyroAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getLinAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getLinAccelX), (20 bytes).
    Removing bno08x_hal.o(i.getLinAccelY), (20 bytes).
    Removing bno08x_hal.o(i.getLinAccelZ), (20 bytes).
    Removing bno08x_hal.o(i.getMagAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getMagX), (20 bytes).
    Removing bno08x_hal.o(i.getMagY), (20 bytes).
    Removing bno08x_hal.o(i.getMagZ), (20 bytes).
    Removing bno08x_hal.o(i.getQ1), (28 bytes).
    Removing bno08x_hal.o(i.getQ2), (28 bytes).
    Removing bno08x_hal.o(i.getQ3), (28 bytes).
    Removing bno08x_hal.o(i.getQuatAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getQuatRadianAccuracy), (20 bytes).
    Removing bno08x_hal.o(i.getRange), (36 bytes).
    Removing bno08x_hal.o(i.getResolution), (36 bytes).
    Removing bno08x_hal.o(i.getStabilityClassifier), (12 bytes).
    Removing bno08x_hal.o(i.getStepCount), (12 bytes).
    Removing bno08x_hal.o(i.readFRSdata), (140 bytes).
    Removing bno08x_hal.o(i.readFRSword), (24 bytes).
    Removing bno08x_hal.o(i.resetReason), (48 bytes).
    Removing bno08x_hal.o(i.saveCalibration), (28 bytes).
    Removing bno08x_hal.o(i.sendCalibrateCommand), (64 bytes).
    Removing bno08x_hal.o(i.sendCommand), (48 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(i.Led_Task), (12 bytes).
    Removing led_app.o(.data), (4 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing pid_app.o(.rev16_text), (4 bytes).
    Removing pid_app.o(.revsh_text), (4 bytes).
    Removing pid_app.o(.rrx_text), (6 bytes).
    Removing pid_app.o(i.app_pid_parse_target), (8 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(i.Uart2_Task), (120 bytes).
    Removing uart_app.o(i.Uart4_Task), (120 bytes).
    Removing uart_app.o(i.calc_motor_angle), (44 bytes).
    Removing uart_app.o(i.calc_relative_angle), (72 bytes).
    Removing uart_app.o(i.check_motor_angle_limits), (236 bytes).
    Removing uart_app.o(i.parse_x_motor_data), (748 bytes).
    Removing uart_app.o(i.parse_y_motor_data), (748 bytes).
    Removing servo_app.o(.rev16_text), (4 bytes).
    Removing servo_app.o(.revsh_text), (4 bytes).
    Removing servo_app.o(.rrx_text), (6 bytes).
    Removing servo_app.o(i.Step_Motor_Set_Dis), (84 bytes).
    Removing servo_app.o(i.Step_Motor_Set_Speed), (204 bytes).
    Removing task_app.o(.rev16_text), (4 bytes).
    Removing task_app.o(.revsh_text), (4 bytes).
    Removing task_app.o(.rrx_text), (6 bytes).
    Removing task_app.o(i.generate_perimeter_points), (304 bytes).
    Removing task_app.o(i.pi_parse_data), (496 bytes).
    Removing task_app.o(i.sort_rect_clockwise), (248 bytes).
    Removing maix_app.o(.rev16_text), (4 bytes).
    Removing maix_app.o(.revsh_text), (4 bytes).
    Removing maix_app.o(.rrx_text), (6 bytes).
    Removing maix_app.o(i.MAIX_ParseCamData), (312 bytes).
    Removing maix_app.o(i.calculate_circle_center_from_trajectory), (62 bytes).
    Removing maix_app.o(i.handle_maix_camera_data_parsing), (412 bytes).
    Removing maix_app.o(.bss), (48 bytes).
    Removing maix_app.o(.data), (4 bytes).
    Removing maix_app.o(.data), (1 bytes).
    Removing bno08x_app.o(.rev16_text), (4 bytes).
    Removing bno08x_app.o(.revsh_text), (4 bytes).
    Removing bno08x_app.o(.rrx_text), (6 bytes).
    Removing feedforward_app.o(.rev16_text), (4 bytes).
    Removing feedforward_app.o(.revsh_text), (4 bytes).
    Removing feedforward_app.o(.rrx_text), (6 bytes).
    Removing feedforward_app.o(i.feedforward_set_physical_params), (16 bytes).
    Removing dneg.o(.text), (6 bytes).

576 unused section(s) (total 46458 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strrchr.c        0x00000000   Number         0  strrchr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/microlib/fpstat.c               0x00000000   Number         0  fpstat.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf_x.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf_x.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ..\APP\bno08x_app.c                      0x00000000   Number         0  bno08x_app.o ABSOLUTE
    ..\APP\feedforward_app.c                 0x00000000   Number         0  feedforward_app.o ABSOLUTE
    ..\APP\key_app.c                         0x00000000   Number         0  key_app.o ABSOLUTE
    ..\APP\led_app.c                         0x00000000   Number         0  led_app.o ABSOLUTE
    ..\APP\maix_app.c                        0x00000000   Number         0  maix_app.o ABSOLUTE
    ..\APP\pid_app.c                         0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\servo_app.c                       0x00000000   Number         0  servo_app.o ABSOLUTE
    ..\APP\task_app.c                        0x00000000   Number         0  task_app.o ABSOLUTE
    ..\APP\uart_app.c                        0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\Components\LED\led_driver.c           0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\Components\PID\pid.c                  0x00000000   Number         0  pid.o ABSOLUTE
    ..\Components\Servo\Emm_V5.c             0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\Components\Uart\ringbuffer.c          0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Components\Uart\uart_driver.c         0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\Components\bno08x\bno08x_hal.c        0x00000000   Number         0  bno08x_hal.o ABSOLUTE
    ..\Components\bno08x\bno08x_hardware_reset_example.c 0x00000000   Number         0  bno08x_hardware_reset_example.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\APP\\bno08x_app.c                    0x00000000   Number         0  bno08x_app.o ABSOLUTE
    ..\\APP\\feedforward_app.c               0x00000000   Number         0  feedforward_app.o ABSOLUTE
    ..\\APP\\key_app.c                       0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\APP\\led_app.c                       0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\APP\\maix_app.c                      0x00000000   Number         0  maix_app.o ABSOLUTE
    ..\\APP\\pid_app.c                       0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\servo_app.c                     0x00000000   Number         0  servo_app.o ABSOLUTE
    ..\\APP\\task_app.c                      0x00000000   Number         0  task_app.o ABSOLUTE
    ..\\APP\\uart_app.c                      0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\Components\\LED\\led_driver.c        0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\\Components\\PID\\pid.c               0x00000000   Number         0  pid.o ABSOLUTE
    ..\\Components\\Servo\\Emm_V5.c          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\\Components\\Uart\\uart_driver.c      0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\\Components\\bno08x\\bno08x_hal.c     0x00000000   Number         0  bno08x_hal.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000226   Section        0  memcpya.o(.text)
    .text                                    0x0800024a   Section        0  memseta.o(.text)
    .text                                    0x0800026e   Section        0  strncmp.o(.text)
    .text                                    0x0800028c   Section        0  __0sscanf.o(.text)
    .text                                    0x080002c4   Section        0  _scanf_int.o(.text)
    .text                                    0x08000410   Section        0  dadd.o(.text)
    .text                                    0x0800055e   Section        0  dmul.o(.text)
    .text                                    0x08000642   Section        0  ddiv.o(.text)
    .text                                    0x08000720   Section        0  dflti.o(.text)
    .text                                    0x08000742   Section        0  dfixi.o(.text)
    .text                                    0x08000780   Section        0  f2d.o(.text)
    .text                                    0x080007a6   Section        0  uidiv.o(.text)
    .text                                    0x080007d2   Section        0  llshl.o(.text)
    .text                                    0x080007f0   Section        0  llushr.o(.text)
    .text                                    0x08000810   Section        0  llsshr.o(.text)
    .text                                    0x08000834   Section        0  _chval.o(.text)
    .text                                    0x08000850   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000851   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000878   Section        0  _sgetc.o(.text)
    .text                                    0x080008b8   Section        0  iusefp.o(.text)
    .text                                    0x080008b8   Section        0  depilogue.o(.text)
    .text                                    0x08000974   Section        0  drnd.o(.text)
    .text                                    0x080009fc   Section        0  dfixul.o(.text)
    .text                                    0x08000a2c   Section       48  cdcmple.o(.text)
    .text                                    0x08000a5c   Section       48  cdrcmple.o(.text)
    .text                                    0x08000a8c   Section        0  fpstat.o(.text)
    .text                                    0x08000a90   Section       36  init.o(.text)
    .text                                    0x08000ab4   Section        0  ctype_o.o(.text)
    .text                                    0x08000abc   Section        0  isspace_o.o(.text)
    .text                                    0x08000ad0   Section        0  _scanf.o(.text)
    .text                                    0x08000e00   Section        0  dfltui.o(.text)
    .text                                    0x08000e1a   Section        0  __dczerorl2.o(.text)
    i.BNO080_HardwareReset                   0x08000e70   Section        0  bno08x_hal.o(i.BNO080_HardwareReset)
    i.BNO080_Init                            0x080010b8   Section        0  bno08x_hal.o(i.BNO080_Init)
    i.BusFault_Handler                       0x080010c4   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x080010c8   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream2_IRQHandler                0x080010d4   Section        0  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x080010e0   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream1_IRQHandler                0x080010ec   Section        0  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x080010f8   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08001104   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08001105   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x0800112c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x0800112d   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08001180   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001181   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080011a8   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x080011aa   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Modify_Vel_Scale                0x080011e2   Section        0  emm_v5.o(i.Emm_V5_Modify_Vel_Scale)
    i.Emm_V5_Origin_Trigger_Return           0x0800121a   Section        0  emm_v5.o(i.Emm_V5_Origin_Trigger_Return)
    i.Emm_V5_Pos_Control                     0x0800124c   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Emm_V5_Read_Sys_Params                 0x080012bc   Section        0  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    i.Emm_V5_Reset_CurPos_To_Zero            0x0800134a   Section        0  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    i.Emm_V5_Stop_Now                        0x0800137a   Section        0  emm_v5.o(i.Emm_V5_Stop_Now)
    i.Emm_V5_Vel_Control                     0x080013ae   Section        0  emm_v5.o(i.Emm_V5_Vel_Control)
    i.Error_Handler                          0x080013f2   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x080013f6   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001488   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x080014ac   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x0800164c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08001720   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08001790   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x080017b4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080019a4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080019ae   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080019b8   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x080019c4   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Master_Receive                 0x08001b4c   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    i.HAL_I2C_Master_Transmit                0x08001d3c   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MspInit                        0x08001e68   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08001f14   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001f24   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001f58   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001f98   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001fc8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001fe4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002024   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002048   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x0800217c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800219c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080021bc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800221c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08002588   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x080025b0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x080025b2   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x080025b4   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08002608   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08002698   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080026f4   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002748   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x080027c8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x080028a4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08002948   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_IC_CaptureCallback             0x080029ec   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080029ee   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08002b20   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08002b74   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08002b76   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08002c42   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08002c9c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08002c9e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08002ca0   Section        0  scheduler.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08002d00   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08002d02   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08002d4c   Section        0  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002ebc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08002f2c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002f30   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080031b0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003214   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08003514   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08003516   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08003518   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080035b8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080035ba   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x080035bc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x080035bd   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterRequestRead                  0x080035ec   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead)
    I2C_MasterRequestRead                    0x080035ed   Thumb Code   230  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead)
    i.I2C_MasterRequestWrite                 0x080036d8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x080036d9   Thumb Code   150  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08003774   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08003775   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x080037cc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x080037cd   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x0800385c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800385d   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08003918   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08003919   Thumb Code   112  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08003988   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08003989   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.Led_Init                               0x080039de   Section        0  led_app.o(i.Led_Init)
    i.MX_DMA_Init                            0x080039e0   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08003a5c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08003bb8   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x08003bf8   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM1_Init                           0x08003c38   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08003d10   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08003d78   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08003de4   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART4_Init                          0x08003e50   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_UART5_Init                          0x08003e88   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08003ec0   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08003ef8   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART6_UART_Init                    0x08003f30   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x08003f68   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08003f6a   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08003f6c   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.QuaternionToEulerAngles                0x08003f70   Section        0  bno08x_hal.o(i.QuaternionToEulerAngles)
    i.SVC_Handler                            0x08004104   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Scheduler_Init                         0x08004108   Section        0  scheduler.o(i.Scheduler_Init)
    i.Scheduler_Run                          0x0800411c   Section        0  scheduler.o(i.Scheduler_Run)
    i.Step_Motor_Init                        0x08004158   Section        0  servo_app.o(i.Step_Motor_Init)
    i.Step_Motor_Return                      0x0800419c   Section        0  servo_app.o(i.Step_Motor_Return)
    i.Step_Motor_Set_Speed_my                0x080041c0   Section        0  servo_app.o(i.Step_Motor_Set_Speed_my)
    i.Step_Motor_Stop                        0x0800428c   Section        0  servo_app.o(i.Step_Motor_Stop)
    i.SysTick_Handler                        0x080042b0   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080042b4   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08004348   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_Init                            0x08004358   Section        0  scheduler.o(i.System_Init)
    i.TIM2_IRQHandler                        0x080043f4   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x08004400   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x080044d0   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080044e4   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080044e5   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x080044f4   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080044f5   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08004554   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x080045c0   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x080045c1   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08004628   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08004629   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08004678   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08004679   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x0800469a   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x0800469b   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART4_IRQHandler                       0x080046c0   Section        0  stm32f4xx_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x080046cc   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x080046d8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080046d9   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080046e6   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080046e7   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08004730   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08004731   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x080047b6   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x080047b7   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x080047d4   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080047d5   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08004822   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08004823   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x0800483e   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800483f   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08004900   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08004901   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08004a0c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x08004aac   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08004aad   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08004b20   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08004b2c   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART6_IRQHandler                      0x08004b38   Section        0  stm32f4xx_it.o(i.USART6_IRQHandler)
    i.Uart1_Task                             0x08004b44   Section        0  uart_app.o(i.Uart1_Task)
    i.Uart5_Task                             0x08004ba0   Section        0  uart_app.o(i.Uart5_Task)
    i.Uart_Init                              0x08004be8   Section        0  uart_driver.o(i.Uart_Init)
    i.UsageFault_Handler                     0x08004cdc   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x08004ce0   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassify                       0x08004d14   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x08004d44   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__ARM_isinff                           0x08004d6a   Section        0  bno08x_hal.o(i.__ARM_isinff)
    __ARM_isinff                             0x08004d6b   Thumb Code    18  bno08x_hal.o(i.__ARM_isinff)
    i.__ARM_isnanf                           0x08004d7c   Section        0  bno08x_hal.o(i.__ARM_isnanf)
    __ARM_isnanf                             0x08004d7d   Thumb Code    12  bno08x_hal.o(i.__ARM_isnanf)
    i.__NVIC_SetPriority                     0x08004d88   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08004d89   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_asinf                         0x08004da8   Section        0  asinf.o(i.__hardfp_asinf)
    i.__hardfp_atan2f                        0x08004ed4   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_cos                           0x08005180   Section        0  cos.o(i.__hardfp_cos)
    i.__hardfp_powf                          0x08005248   Section        0  powf.o(i.__hardfp_powf)
    i.__hardfp_round                         0x080058b0   Section        0  round.o(i.__hardfp_round)
    i.__hardfp_sin                           0x08005990   Section        0  sin.o(i.__hardfp_sin)
    i.__hardfp_sqrtf                         0x08005a58   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__ieee754_rem_pio2                     0x08005a98   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x08005ed0   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x08006040   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x08006138   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x08006268   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x08006280   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x080062a0   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_flt_divzero                  0x080062c0   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x080062d4   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x080062da   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x080062e0   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_overflow                 0x080062f0   Section        0  funder.o(i.__mathlib_flt_overflow)
    i.__mathlib_flt_underflow                0x08006300   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__scatterload_copy                     0x08006310   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800631e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08006320   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08006330   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x0800633c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800633d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080064c0   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080064c1   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08006b74   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08006b75   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08006b98   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08006b99   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08006bc6   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08006bc7   Thumb Code    22  printfa.o(i._snputc)
    i.app_pid_calc                           0x08006bdc   Section        0  pid_app.o(i.app_pid_calc)
    i.app_pid_init                           0x08006d48   Section        0  pid_app.o(i.app_pid_init)
    i.app_pid_parse_cmd                      0x08006d90   Section        0  pid_app.o(i.app_pid_parse_cmd)
    i.app_pid_set_target                     0x080071a8   Section        0  pid_app.o(i.app_pid_set_target)
    i.app_pid_set_x_params                   0x080071dc   Section        0  pid_app.o(i.app_pid_set_x_params)
    i.app_pid_set_y_params                   0x08007210   Section        0  pid_app.o(i.app_pid_set_y_params)
    i.app_pid_start                          0x08007244   Section        0  pid_app.o(i.app_pid_start)
    i.app_pid_stop                           0x0800728c   Section        0  pid_app.o(i.app_pid_stop)
    i.app_pid_task                           0x080072c0   Section        0  pid_app.o(i.app_pid_task)
    i.app_pid_update_position                0x080072d4   Section        0  pid_app.o(i.app_pid_update_position)
    i.bno080_task                            0x08007310   Section        0  bno08x_app.o(i.bno080_task)
    i.calculate_and_store_perimeter_points   0x08007444   Section        0  uart_app.o(i.calculate_and_store_perimeter_points)
    i.convert_to_continuous_yaw              0x08007828   Section        0  bno08x_app.o(i.convert_to_continuous_yaw)
    i.dataAvailable                          0x08007888   Section        0  bno08x_hal.o(i.dataAvailable)
    i.enableAccelerometer                    0x080078b8   Section        0  bno08x_hal.o(i.enableAccelerometer)
    i.enableGyro                             0x080078c2   Section        0  bno08x_hal.o(i.enableGyro)
    i.enableRotationVector                   0x080078cc   Section        0  bno08x_hal.o(i.enableRotationVector)
    i.fabs                                   0x080078d6   Section        0  fabs.o(i.fabs)
    i.feedforward_calculate_compensation     0x080078f0   Section        0  feedforward_app.o(i.feedforward_calculate_compensation)
    i.feedforward_calibrate                  0x080079e8   Section        0  feedforward_app.o(i.feedforward_calibrate)
    i.feedforward_get_compensation           0x08007bc4   Section        0  feedforward_app.o(i.feedforward_get_compensation)
    i.feedforward_identify_motion_state      0x08007be4   Section        0  feedforward_app.o(i.feedforward_identify_motion_state)
    i.feedforward_init                       0x08007c40   Section        0  feedforward_app.o(i.feedforward_init)
    i.feedforward_print_compensation         0x08007cc8   Section        0  feedforward_app.o(i.feedforward_print_compensation)
    i.feedforward_print_status               0x08007d24   Section        0  feedforward_app.o(i.feedforward_print_status)
    i.feedforward_process                    0x08007d88   Section        0  feedforward_app.o(i.feedforward_process)
    i.feedforward_set_parameters             0x08007e94   Section        0  feedforward_app.o(i.feedforward_set_parameters)
    i.feedforward_task                       0x08007ea0   Section        0  feedforward_app.o(i.feedforward_task)
    i.feedforward_update_imu_data            0x08007ea4   Section        0  feedforward_app.o(i.feedforward_update_imu_data)
    i.free                                   0x08007f10   Section        0  malloc.o(i.free)
    i.generateCirclePoints                   0x08007f60   Section        0  task_app.o(i.generateCirclePoints)
    i.getAccelX                              0x08008048   Section        0  bno08x_hal.o(i.getAccelX)
    i.getAccelY                              0x0800805c   Section        0  bno08x_hal.o(i.getAccelY)
    i.getAccelZ                              0x08008070   Section        0  bno08x_hal.o(i.getAccelZ)
    i.getGyroX                               0x08008084   Section        0  bno08x_hal.o(i.getGyroX)
    i.getGyroY                               0x08008098   Section        0  bno08x_hal.o(i.getGyroY)
    i.getGyroZ                               0x080080ac   Section        0  bno08x_hal.o(i.getGyroZ)
    i.getQuatI                               0x080080c0   Section        0  bno08x_hal.o(i.getQuatI)
    i.getQuatJ                               0x080080d4   Section        0  bno08x_hal.o(i.getQuatJ)
    i.getQuatK                               0x080080e8   Section        0  bno08x_hal.o(i.getQuatK)
    i.getQuatReal                            0x080080fc   Section        0  bno08x_hal.o(i.getQuatReal)
    i.get_pitch                              0x08008110   Section        0  bno08x_app.o(i.get_pitch)
    i.get_roll                               0x0800811c   Section        0  bno08x_app.o(i.get_roll)
    i.get_yaw                                0x08008128   Section        0  bno08x_app.o(i.get_yaw)
    i.init_vision_data                       0x08008138   Section        0  maix_app.o(i.init_vision_data)
    i.key_read                               0x08008150   Section        0  key_app.o(i.key_read)
    i.key_task                               0x08008190   Section        0  key_app.o(i.key_task)
    i.main                                   0x080081d8   Section        0  main.o(i.main)
    i.maixcam_task                           0x08008220   Section        0  maix_app.o(i.maixcam_task)
    i.malloc                                 0x080082c8   Section        0  malloc.o(i.malloc)
    i.my_bno080_init                         0x08008334   Section        0  bno08x_app.o(i.my_bno080_init)
    i.my_printf                              0x08008444   Section        0  uart_driver.o(i.my_printf)
    i.parseInputReport                       0x08008478   Section        0  bno08x_hal.o(i.parseInputReport)
    i.parse_uart_message                     0x08008550   Section        0  maix_app.o(i.parse_uart_message)
    i.pid_app_limit_integral                 0x08008604   Section        0  pid.o(i.pid_app_limit_integral)
    i.pid_calculate_positional               0x08008628   Section        0  pid.o(i.pid_calculate_positional)
    i.pid_circle                             0x08008690   Section        0  pid_app.o(i.pid_circle)
    i.pid_constrain                          0x08008784   Section        0  pid.o(i.pid_constrain)
    i.pid_follow                             0x080087a4   Section        0  pid_app.o(i.pid_follow)
    i.pid_init                               0x08008828   Section        0  pid.o(i.pid_init)
    i.pid_ju                                 0x08008858   Section        0  pid_app.o(i.pid_ju)
    i.pid_only_target                        0x0800894c   Section        0  pid_app.o(i.pid_only_target)
    i.pid_out_limit                          0x080089dc   Section        0  pid.o(i.pid_out_limit)
    pid_out_limit                            0x080089dd   Thumb Code    38  pid.o(i.pid_out_limit)
    i.pid_reset                              0x08008a04   Section        0  pid.o(i.pid_reset)
    i.pid_set_limit                          0x08008a2c   Section        0  pid.o(i.pid_set_limit)
    i.pid_set_params                         0x08008a32   Section        0  pid.o(i.pid_set_params)
    i.pid_set_target                         0x08008a38   Section        0  pid.o(i.pid_set_target)
    i.process_command                        0x08008a40   Section        0  uart_app.o(i.process_command)
    i.process_reset_command                  0x08008c84   Section        0  uart_app.o(i.process_reset_command)
    i.qToFloat                               0x08008d24   Section        0  bno08x_hal.o(i.qToFloat)
    i.receivePacket                          0x08008d50   Section        0  bno08x_hal.o(i.receivePacket)
    receivePacket                            0x08008d51   Thumb Code   176  bno08x_hal.o(i.receivePacket)
    i.rt_ringbuffer_data_len                 0x08008e28   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08008e58   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x08008ec6   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x08008eec   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08008f5e   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    i.save_initial_position                  0x08008f80   Section        0  uart_app.o(i.save_initial_position)
    i.sendPacket                             0x08008fe0   Section        0  bno08x_hal.o(i.sendPacket)
    sendPacket                               0x08008fe1   Thumb Code   102  bno08x_hal.o(i.sendPacket)
    i.setFeatureCommand                      0x08009050   Section        0  bno08x_hal.o(i.setFeatureCommand)
    i.softReset                              0x0800909c   Section        0  bno08x_hal.o(i.softReset)
    i.sqrtf                                  0x080090d0   Section        0  sqrtf.o(i.sqrtf)
    .constdata                               0x0800910e   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x0800910e   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08009116   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009126   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009130   Section       16  feedforward_app.o(.constdata)
    .constdata                               0x08009140   Section      320  powf.o(.constdata)
    table                                    0x08009140   Data         128  powf.o(.constdata)
    powersof2to1over16top                    0x080091c0   Data          64  powf.o(.constdata)
    powersof2to1over16bot                    0x08009200   Data          64  powf.o(.constdata)
    powersof2to1over16all                    0x08009240   Data          64  powf.o(.constdata)
    .constdata                               0x08009280   Section       48  cos_i.o(.constdata)
    C                                        0x08009280   Data          48  cos_i.o(.constdata)
    .constdata                               0x080092b0   Section      200  rred.o(.constdata)
    pio2s                                    0x080092b0   Data          48  rred.o(.constdata)
    twooverpi                                0x080092e0   Data         152  rred.o(.constdata)
    .constdata                               0x08009378   Section       40  sin_i.o(.constdata)
    S                                        0x08009378   Data          40  sin_i.o(.constdata)
    .constdata                               0x080093a0   Section      129  ctype_o.o(.constdata)
    .constdata                               0x08009424   Section        4  ctype_o.o(.constdata)
    table                                    0x08009424   Data           4  ctype_o.o(.constdata)
    .conststring                             0x08009428   Section       66  uart_app.o(.conststring)
    .conststring                             0x0800946c   Section       29  feedforward_app.o(.conststring)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       90  bno08x_hal.o(.data)
    commandSequenceNumber                    0x20000010   Data           1  bno08x_hal.o(.data)
    _deviceAddress                           0x20000011   Data           1  bno08x_hal.o(.data)
    accelAccuracy                            0x20000012   Data           1  bno08x_hal.o(.data)
    accelLinAccuracy                         0x20000013   Data           1  bno08x_hal.o(.data)
    gyroAccuracy                             0x20000014   Data           1  bno08x_hal.o(.data)
    magAccuracy                              0x20000015   Data           1  bno08x_hal.o(.data)
    quatAccuracy                             0x20000016   Data           1  bno08x_hal.o(.data)
    stabilityClassifier                      0x20000017   Data           1  bno08x_hal.o(.data)
    activityClassifier                       0x20000018   Data           1  bno08x_hal.o(.data)
    rawAccelX                                0x2000001a   Data           2  bno08x_hal.o(.data)
    rawAccelY                                0x2000001c   Data           2  bno08x_hal.o(.data)
    rawAccelZ                                0x2000001e   Data           2  bno08x_hal.o(.data)
    rawLinAccelX                             0x20000020   Data           2  bno08x_hal.o(.data)
    rawLinAccelY                             0x20000022   Data           2  bno08x_hal.o(.data)
    rawLinAccelZ                             0x20000024   Data           2  bno08x_hal.o(.data)
    rawGyroX                                 0x20000026   Data           2  bno08x_hal.o(.data)
    rawGyroY                                 0x20000028   Data           2  bno08x_hal.o(.data)
    rawGyroZ                                 0x2000002a   Data           2  bno08x_hal.o(.data)
    rawMagX                                  0x2000002c   Data           2  bno08x_hal.o(.data)
    rawMagY                                  0x2000002e   Data           2  bno08x_hal.o(.data)
    rawMagZ                                  0x20000030   Data           2  bno08x_hal.o(.data)
    rawQuatI                                 0x20000032   Data           2  bno08x_hal.o(.data)
    rawQuatJ                                 0x20000034   Data           2  bno08x_hal.o(.data)
    rawQuatK                                 0x20000036   Data           2  bno08x_hal.o(.data)
    rawQuatReal                              0x20000038   Data           2  bno08x_hal.o(.data)
    rawQuatRadianAccuracy                    0x2000003a   Data           2  bno08x_hal.o(.data)
    stepCount                                0x2000003c   Data           2  bno08x_hal.o(.data)
    _activityConfidences                     0x20000040   Data           4  bno08x_hal.o(.data)
    rotationVector_Q1                        0x20000044   Data           4  bno08x_hal.o(.data)
    accelerometer_Q1                         0x20000048   Data           4  bno08x_hal.o(.data)
    linear_accelerometer_Q1                  0x2000004c   Data           4  bno08x_hal.o(.data)
    gyro_Q1                                  0x20000050   Data           4  bno08x_hal.o(.data)
    magnetometer_Q1                          0x20000054   Data           4  bno08x_hal.o(.data)
    i2c_error_count                          0x20000058   Data           4  bno08x_hal.o(.data)
    hi2c_bno080                              0x2000005c   Data           4  bno08x_hal.o(.data)
    shtpHeader                               0x20000060   Data           4  bno08x_hal.o(.data)
    sequenceNumber                           0x20000064   Data           6  bno08x_hal.o(.data)
    .data                                    0x2000006c   Section       28  scheduler.o(.data)
    scheduler_task                           0x20000070   Data          24  scheduler.o(.data)
    .data                                    0x20000088   Section        1  key_app.o(.data)
    .data                                    0x20000089   Section        4  key_app.o(.data)
    .data                                    0x20000090   Section       84  pid_app.o(.data)
    .data                                    0x200000e4   Section       44  uart_app.o(.data)
    .data                                    0x20000110   Section        1  servo_app.o(.data)
    .data                                    0x20000114   Section      104  task_app.o(.data)
    .data                                    0x2000017c   Section       32  bno08x_app.o(.data)
    debug_counter                            0x2000018c   Data           4  bno08x_app.o(.data)
    .data                                    0x2000019c   Section        4  feedforward_app.o(.data)
    debug_counter                            0x2000019c   Data           4  feedforward_app.o(.data)
    .data                                    0x200001a0   Section        4  mvars.o(.data)
    .data                                    0x200001a4   Section        4  mvars.o(.data)
    .data                                    0x200001a8   Section        4  errno.o(.data)
    _errno                                   0x200001a8   Data           4  errno.o(.data)
    .bss                                     0x200001ac   Section      168  i2c.o(.bss)
    .bss                                     0x20000254   Section      288  tim.o(.bss)
    .bss                                     0x20000374   Section      840  usart.o(.bss)
    .bss                                     0x200006bc   Section     1340  uart_driver.o(.bss)
    .bss                                     0x20000bf8   Section      128  uart_driver.o(.bss)
    .bss                                     0x20000c78   Section      128  uart_driver.o(.bss)
    .bss                                     0x20000cf8   Section      128  uart_driver.o(.bss)
    .bss                                     0x20000d78   Section      164  bno08x_hal.o(.bss)
    shtpData                                 0x20000d78   Data         128  bno08x_hal.o(.bss)
    metaData                                 0x20000df8   Data          36  bno08x_hal.o(.bss)
    .bss                                     0x20000e1c   Section       10  bno08x_hal.o(.bss)
    activityConfidences                      0x20000e1c   Data          10  bno08x_hal.o(.bss)
    .bss                                     0x20000e28   Section      120  pid_app.o(.bss)
    .bss                                     0x20000ea0   Section      268  uart_app.o(.bss)
    .bss                                     0x20000fac   Section      164  task_app.o(.bss)
    .bss                                     0x20001050   Section      174  maix_app.o(.bss)
    .bss                                     0x20001100   Section      140  feedforward_app.o(.bss)
    HEAP                                     0x20001190   Section     1536  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20001790   Section     4096  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000227   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000227   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000227   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800024b   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800024b   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800024b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000259   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000259   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000259   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800025d   Thumb Code    18  memseta.o(.text)
    strncmp                                  0x0800026f   Thumb Code    30  strncmp.o(.text)
    __0sscanf                                0x0800028d   Thumb Code    48  __0sscanf.o(.text)
    _scanf_int                               0x080002c5   Thumb Code   332  _scanf_int.o(.text)
    __aeabi_dadd                             0x08000411   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000553   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000559   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800055f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000643   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x08000721   Thumb Code    34  dflti.o(.text)
    __aeabi_d2iz                             0x08000743   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x08000781   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x080007a7   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080007a7   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080007d3   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080007d3   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080007f1   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080007f1   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000811   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000811   Thumb Code     0  llsshr.o(.text)
    _chval                                   0x08000835   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x0800085d   Thumb Code    20  scanf_char.o(.text)
    _sgetc                                   0x08000879   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000897   Thumb Code    34  _sgetc.o(.text)
    __I$use$fp                               0x080008b9   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x080008b9   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080008d7   Thumb Code   156  depilogue.o(.text)
    _drnd                                    0x08000975   Thumb Code   132  drnd.o(.text)
    __aeabi_d2ulz                            0x080009fd   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdcmpeq                          0x08000a2d   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000a2d   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x08000a5d   Thumb Code    48  cdrcmple.o(.text)
    __fp_status                              0x08000a8d   Thumb Code     4  fpstat.o(.text)
    __ieee_status                            0x08000a8d   Thumb Code     0  fpstat.o(.text)
    __scatterload                            0x08000a91   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000a91   Thumb Code     0  init.o(.text)
    __rt_ctype_table                         0x08000ab5   Thumb Code     4  ctype_o.o(.text)
    isspace                                  0x08000abd   Thumb Code    18  isspace_o.o(.text)
    __vfscanf                                0x08000ad1   Thumb Code   810  _scanf.o(.text)
    __aeabi_ui2d                             0x08000e01   Thumb Code    26  dfltui.o(.text)
    __decompress                             0x08000e1b   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000e1b   Thumb Code    86  __dczerorl2.o(.text)
    BNO080_HardwareReset                     0x08000e71   Thumb Code   320  bno08x_hal.o(i.BNO080_HardwareReset)
    BNO080_Init                              0x080010b9   Thumb Code     8  bno08x_hal.o(i.BNO080_Init)
    BusFault_Handler                         0x080010c5   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x080010c9   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream2_IRQHandler                  0x080010d5   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x080010e1   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x080010ed   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x080010f9   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x080011a9   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x080011ab   Thumb Code    56  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Modify_Vel_Scale                  0x080011e3   Thumb Code    56  emm_v5.o(i.Emm_V5_Modify_Vel_Scale)
    Emm_V5_Origin_Trigger_Return             0x0800121b   Thumb Code    50  emm_v5.o(i.Emm_V5_Origin_Trigger_Return)
    Emm_V5_Pos_Control                       0x0800124d   Thumb Code   112  emm_v5.o(i.Emm_V5_Pos_Control)
    Emm_V5_Read_Sys_Params                   0x080012bd   Thumb Code   142  emm_v5.o(i.Emm_V5_Read_Sys_Params)
    Emm_V5_Reset_CurPos_To_Zero              0x0800134b   Thumb Code    48  emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero)
    Emm_V5_Stop_Now                          0x0800137b   Thumb Code    52  emm_v5.o(i.Emm_V5_Stop_Now)
    Emm_V5_Vel_Control                       0x080013af   Thumb Code    68  emm_v5.o(i.Emm_V5_Vel_Control)
    Error_Handler                            0x080013f3   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x080013f7   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001489   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x080014ad   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x0800164d   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08001721   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08001791   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x080017b5   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080019a5   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080019af   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080019b9   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x080019c5   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Master_Receive                   0x08001b4d   Thumb Code   482  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    HAL_I2C_Master_Transmit                  0x08001d3d   Thumb Code   290  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MspInit                          0x08001e69   Thumb Code   154  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08001f15   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001f25   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001f59   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001f99   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001fc9   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001fe5   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002025   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002049   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x0800217d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800219d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080021bd   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800221d   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08002589   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x080025b1   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x080025b3   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x080025b5   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08002609   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002699   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080026f5   Thumb Code    76  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002749   Thumb Code   100  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x080027c9   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x080028a5   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08002949   Thumb Code   142  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_IC_CaptureCallback               0x080029ed   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080029ef   Thumb Code   304  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08002b21   Thumb Code    72  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08002b75   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08002b77   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002c43   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002c9d   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08002c9f   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08002ca1   Thumb Code    88  scheduler.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08002d01   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08002d03   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002d4d   Thumb Code   288  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002ebd   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002f2d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002f31   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080031b1   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003215   Thumb Code   716  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08003515   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08003517   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08003519   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080035b9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080035bb   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    Led_Init                                 0x080039df   Thumb Code     2  led_app.o(i.Led_Init)
    MX_DMA_Init                              0x080039e1   Thumb Code   120  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08003a5d   Thumb Code   328  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08003bb9   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x08003bf9   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_TIM1_Init                             0x08003c39   Thumb Code   206  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08003d11   Thumb Code    98  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08003d79   Thumb Code    98  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08003de5   Thumb Code    98  tim.o(i.MX_TIM4_Init)
    MX_UART4_Init                            0x08003e51   Thumb Code    48  usart.o(i.MX_UART4_Init)
    MX_UART5_Init                            0x08003e89   Thumb Code    48  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08003ec1   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08003ef9   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART6_UART_Init                      0x08003f31   Thumb Code    48  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x08003f69   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08003f6b   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08003f6d   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    QuaternionToEulerAngles                  0x08003f71   Thumb Code   384  bno08x_hal.o(i.QuaternionToEulerAngles)
    SVC_Handler                              0x08004105   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Scheduler_Init                           0x08004109   Thumb Code    14  scheduler.o(i.Scheduler_Init)
    Scheduler_Run                            0x0800411d   Thumb Code    56  scheduler.o(i.Scheduler_Run)
    Step_Motor_Init                          0x08004159   Thumb Code    58  servo_app.o(i.Step_Motor_Init)
    Step_Motor_Return                        0x0800419d   Thumb Code    28  servo_app.o(i.Step_Motor_Return)
    Step_Motor_Set_Speed_my                  0x080041c1   Thumb Code   178  servo_app.o(i.Step_Motor_Set_Speed_my)
    Step_Motor_Stop                          0x0800428d   Thumb Code    26  servo_app.o(i.Step_Motor_Stop)
    SysTick_Handler                          0x080042b1   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080042b5   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x08004349   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    System_Init                              0x08004359   Thumb Code    88  scheduler.o(i.System_Init)
    TIM2_IRQHandler                          0x080043f5   Thumb Code     6  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x08004401   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x080044d1   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08004555   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART4_IRQHandler                         0x080046c1   Thumb Code     6  stm32f4xx_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x080046cd   Thumb Code     6  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x08004a0d   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08004b21   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08004b2d   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART6_IRQHandler                        0x08004b39   Thumb Code     6  stm32f4xx_it.o(i.USART6_IRQHandler)
    Uart1_Task                               0x08004b45   Thumb Code    68  uart_app.o(i.Uart1_Task)
    Uart5_Task                               0x08004ba1   Thumb Code    48  uart_app.o(i.Uart5_Task)
    Uart_Init                                0x08004be9   Thumb Code   184  uart_driver.o(i.Uart_Init)
    UsageFault_Handler                       0x08004cdd   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x08004ce1   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08004ce1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08004ce1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08004ce1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08004ce1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassify                         0x08004d15   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x08004d45   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_asinf                           0x08004da9   Thumb Code   258  asinf.o(i.__hardfp_asinf)
    __hardfp_atan2f                          0x08004ed5   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_cos                             0x08005181   Thumb Code   180  cos.o(i.__hardfp_cos)
    __hardfp_powf                            0x08005249   Thumb Code  1606  powf.o(i.__hardfp_powf)
    __mathlib_powf                           0x08005249   Thumb Code     0  powf.o(i.__hardfp_powf)
    __hardfp_round                           0x080058b1   Thumb Code   194  round.o(i.__hardfp_round)
    __hardfp_sin                             0x08005991   Thumb Code   180  sin.o(i.__hardfp_sin)
    __hardfp_sqrtf                           0x08005a59   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __ieee754_rem_pio2                       0x08005a99   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x08005ed1   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x08006041   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x08006139   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x08006269   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x08006281   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x080062a1   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_flt_divzero                    0x080062c1   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x080062d5   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x080062db   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x080062e1   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_overflow                   0x080062f1   Thumb Code    10  funder.o(i.__mathlib_flt_overflow)
    __mathlib_flt_underflow                  0x08006301   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __scatterload_copy                       0x08006311   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800631f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08006321   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08006331   Thumb Code     6  errno.o(i.__set_errno)
    app_pid_calc                             0x08006bdd   Thumb Code   322  pid_app.o(i.app_pid_calc)
    app_pid_init                             0x08006d49   Thumb Code    62  pid_app.o(i.app_pid_init)
    app_pid_parse_cmd                        0x08006d91   Thumb Code   646  pid_app.o(i.app_pid_parse_cmd)
    app_pid_set_target                       0x080071a9   Thumb Code    42  pid_app.o(i.app_pid_set_target)
    app_pid_set_x_params                     0x080071dd   Thumb Code    42  pid_app.o(i.app_pid_set_x_params)
    app_pid_set_y_params                     0x08007211   Thumb Code    42  pid_app.o(i.app_pid_set_y_params)
    app_pid_start                            0x08007245   Thumb Code    38  pid_app.o(i.app_pid_start)
    app_pid_stop                             0x0800728d   Thumb Code    24  pid_app.o(i.app_pid_stop)
    app_pid_task                             0x080072c1   Thumb Code    14  pid_app.o(i.app_pid_task)
    app_pid_update_position                  0x080072d5   Thumb Code    52  pid_app.o(i.app_pid_update_position)
    bno080_task                              0x08007311   Thumb Code   232  bno08x_app.o(i.bno080_task)
    calculate_and_store_perimeter_points     0x08007445   Thumb Code   892  uart_app.o(i.calculate_and_store_perimeter_points)
    convert_to_continuous_yaw                0x08007829   Thumb Code    80  bno08x_app.o(i.convert_to_continuous_yaw)
    dataAvailable                            0x08007889   Thumb Code    38  bno08x_hal.o(i.dataAvailable)
    enableAccelerometer                      0x080078b9   Thumb Code    10  bno08x_hal.o(i.enableAccelerometer)
    enableGyro                               0x080078c3   Thumb Code    10  bno08x_hal.o(i.enableGyro)
    enableRotationVector                     0x080078cd   Thumb Code    10  bno08x_hal.o(i.enableRotationVector)
    fabs                                     0x080078d7   Thumb Code    24  fabs.o(i.fabs)
    feedforward_calculate_compensation       0x080078f1   Thumb Code   234  feedforward_app.o(i.feedforward_calculate_compensation)
    feedforward_calibrate                    0x080079e9   Thumb Code   346  feedforward_app.o(i.feedforward_calibrate)
    feedforward_get_compensation             0x08007bc5   Thumb Code    26  feedforward_app.o(i.feedforward_get_compensation)
    feedforward_identify_motion_state        0x08007be5   Thumb Code    86  feedforward_app.o(i.feedforward_identify_motion_state)
    feedforward_init                         0x08007c41   Thumb Code    88  feedforward_app.o(i.feedforward_init)
    feedforward_print_compensation           0x08007cc9   Thumb Code    42  feedforward_app.o(i.feedforward_print_compensation)
    feedforward_print_status                 0x08007d25   Thumb Code    42  feedforward_app.o(i.feedforward_print_status)
    feedforward_process                      0x08007d89   Thumb Code   206  feedforward_app.o(i.feedforward_process)
    feedforward_set_parameters               0x08007e95   Thumb Code     8  feedforward_app.o(i.feedforward_set_parameters)
    feedforward_task                         0x08007ea1   Thumb Code     4  feedforward_app.o(i.feedforward_task)
    feedforward_update_imu_data              0x08007ea5   Thumb Code   102  feedforward_app.o(i.feedforward_update_imu_data)
    free                                     0x08007f11   Thumb Code    76  malloc.o(i.free)
    generateCirclePoints                     0x08007f61   Thumb Code   214  task_app.o(i.generateCirclePoints)
    getAccelX                                0x08008049   Thumb Code    14  bno08x_hal.o(i.getAccelX)
    getAccelY                                0x0800805d   Thumb Code    14  bno08x_hal.o(i.getAccelY)
    getAccelZ                                0x08008071   Thumb Code    14  bno08x_hal.o(i.getAccelZ)
    getGyroX                                 0x08008085   Thumb Code    14  bno08x_hal.o(i.getGyroX)
    getGyroY                                 0x08008099   Thumb Code    14  bno08x_hal.o(i.getGyroY)
    getGyroZ                                 0x080080ad   Thumb Code    14  bno08x_hal.o(i.getGyroZ)
    getQuatI                                 0x080080c1   Thumb Code    14  bno08x_hal.o(i.getQuatI)
    getQuatJ                                 0x080080d5   Thumb Code    14  bno08x_hal.o(i.getQuatJ)
    getQuatK                                 0x080080e9   Thumb Code    14  bno08x_hal.o(i.getQuatK)
    getQuatReal                              0x080080fd   Thumb Code    14  bno08x_hal.o(i.getQuatReal)
    get_pitch                                0x08008111   Thumb Code     8  bno08x_app.o(i.get_pitch)
    get_roll                                 0x0800811d   Thumb Code     8  bno08x_app.o(i.get_roll)
    get_yaw                                  0x08008129   Thumb Code    10  bno08x_app.o(i.get_yaw)
    init_vision_data                         0x08008139   Thumb Code    18  maix_app.o(i.init_vision_data)
    key_read                                 0x08008151   Thumb Code    58  key_app.o(i.key_read)
    key_task                                 0x08008191   Thumb Code    64  key_app.o(i.key_task)
    main                                     0x080081d9   Thumb Code    70  main.o(i.main)
    maixcam_task                             0x08008221   Thumb Code   144  maix_app.o(i.maixcam_task)
    malloc                                   0x080082c9   Thumb Code    92  malloc.o(i.malloc)
    my_bno080_init                           0x08008335   Thumb Code    86  bno08x_app.o(i.my_bno080_init)
    my_printf                                0x08008445   Thumb Code    50  uart_driver.o(i.my_printf)
    parseInputReport                         0x08008479   Thumb Code   208  bno08x_hal.o(i.parseInputReport)
    parse_uart_message                       0x08008551   Thumb Code   140  maix_app.o(i.parse_uart_message)
    pid_app_limit_integral                   0x08008605   Thumb Code    36  pid.o(i.pid_app_limit_integral)
    pid_calculate_positional                 0x08008629   Thumb Code   102  pid.o(i.pid_calculate_positional)
    pid_circle                               0x08008691   Thumb Code   208  pid_app.o(i.pid_circle)
    pid_constrain                            0x08008785   Thumb Code    32  pid.o(i.pid_constrain)
    pid_follow                               0x080087a5   Thumb Code   102  pid_app.o(i.pid_follow)
    pid_init                                 0x08008829   Thumb Code    42  pid.o(i.pid_init)
    pid_ju                                   0x08008859   Thumb Code   208  pid_app.o(i.pid_ju)
    pid_only_target                          0x0800894d   Thumb Code   116  pid_app.o(i.pid_only_target)
    pid_reset                                0x08008a05   Thumb Code    34  pid.o(i.pid_reset)
    pid_set_limit                            0x08008a2d   Thumb Code     6  pid.o(i.pid_set_limit)
    pid_set_params                           0x08008a33   Thumb Code     6  pid.o(i.pid_set_params)
    pid_set_target                           0x08008a39   Thumb Code     6  pid.o(i.pid_set_target)
    process_command                          0x08008a41   Thumb Code   328  uart_app.o(i.process_command)
    process_reset_command                    0x08008c85   Thumb Code    80  uart_app.o(i.process_reset_command)
    qToFloat                                 0x08008d25   Thumb Code    42  bno08x_hal.o(i.qToFloat)
    rt_ringbuffer_data_len                   0x08008e29   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08008e59   Thumb Code   110  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08008ec7   Thumb Code    38  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08008eed   Thumb Code   114  ringbuffer.o(i.rt_ringbuffer_put)
    rt_ringbuffer_status                     0x08008f5f   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    save_initial_position                    0x08008f81   Thumb Code    48  uart_app.o(i.save_initial_position)
    setFeatureCommand                        0x08009051   Thumb Code    70  bno08x_hal.o(i.setFeatureCommand)
    softReset                                0x0800909d   Thumb Code    46  bno08x_hal.o(i.softReset)
    sqrtf                                    0x080090d1   Thumb Code    62  sqrtf.o(i.sqrtf)
    AHBPrescTable                            0x08009116   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08009126   Data           8  system_stm32f4xx.o(.constdata)
    __ctype_table                            0x080093a0   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x0800948c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080094ac   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    measure_time3ms                          0x2000006c   Data           1  scheduler.o(.data)
    maixcam_time1ms                          0x2000006d   Data           1  scheduler.o(.data)
    key_time10ms                             0x2000006e   Data           1  scheduler.o(.data)
    task_num                                 0x2000006f   Data           1  scheduler.o(.data)
    turn_flag                                0x20000088   Data           1  key_app.o(.data)
    key_val                                  0x20000089   Data           1  key_app.o(.data)
    key_old                                  0x2000008a   Data           1  key_app.o(.data)
    key_down                                 0x2000008b   Data           1  key_app.o(.data)
    key_up                                   0x2000008c   Data           1  key_app.o(.data)
    pid_running                              0x20000090   Data           1  pid_app.o(.data)
    num                                      0x20000092   Data           2  pid_app.o(.data)
    target_x                                 0x20000094   Data           4  pid_app.o(.data)
    target_y                                 0x20000098   Data           4  pid_app.o(.data)
    current_x                                0x2000009c   Data           4  pid_app.o(.data)
    current_y                                0x200000a0   Data           4  pid_app.o(.data)
    pid_params_x                             0x200000a4   Data          32  pid_app.o(.data)
    pid_params_y                             0x200000c4   Data          32  pid_app.o(.data)
    pid_mode                                 0x200000e4   Data           1  uart_app.o(.data)
    x_angle_limit_flag                       0x200000e5   Data           1  uart_app.o(.data)
    y_angle_limit_flag                       0x200000e6   Data           1  uart_app.o(.data)
    motor_angle_limit_check_enabled          0x200000e7   Data           1  uart_app.o(.data)
    x_reference_initialized                  0x200000e8   Data           1  uart_app.o(.data)
    y_reference_initialized                  0x200000e9   Data           1  uart_app.o(.data)
    x_initial_direction                      0x200000ea   Data           1  uart_app.o(.data)
    y_initial_direction                      0x200000eb   Data           1  uart_app.o(.data)
    initial_position_saved                   0x200000ec   Data           1  uart_app.o(.data)
    x_motor_angle                            0x200000f0   Data           4  uart_app.o(.data)
    y_motor_angle                            0x200000f4   Data           4  uart_app.o(.data)
    x_reference_position                     0x200000f8   Data           4  uart_app.o(.data)
    y_reference_position                     0x200000fc   Data           4  uart_app.o(.data)
    x_relative_angle                         0x20000100   Data           4  uart_app.o(.data)
    y_relative_angle                         0x20000104   Data           4  uart_app.o(.data)
    x_initial_position                       0x20000108   Data           4  uart_app.o(.data)
    y_initial_position                       0x2000010c   Data           4  uart_app.o(.data)
    return_flag                              0x20000110   Data           1  servo_app.o(.data)
    latest_red_laser_coord                   0x20000114   Data          16  task_app.o(.data)
    latest_blue_laser_coord                  0x20000124   Data          16  task_app.o(.data)
    in_RectData                              0x20000134   Data          72  task_app.o(.data)
    first_flat                               0x2000017c   Data           1  bno08x_app.o(.data)
    g_is_yaw_initialized                     0x2000017d   Data           1  bno08x_app.o(.data)
    frist_yaw                                0x20000180   Data           4  bno08x_app.o(.data)
    g_last_yaw                               0x20000184   Data           4  bno08x_app.o(.data)
    g_revolution_count                       0x20000188   Data           4  bno08x_app.o(.data)
    roll                                     0x20000190   Data           4  bno08x_app.o(.data)
    pitch                                    0x20000194   Data           4  bno08x_app.o(.data)
    yaw                                      0x20000198   Data           4  bno08x_app.o(.data)
    __microlib_freelist                      0x200001a0   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x200001a4   Data           4  mvars.o(.data)
    hi2c1                                    0x200001ac   Data          84  i2c.o(.bss)
    hi2c2                                    0x20000200   Data          84  i2c.o(.bss)
    htim1                                    0x20000254   Data          72  tim.o(.bss)
    htim2                                    0x2000029c   Data          72  tim.o(.bss)
    htim3                                    0x200002e4   Data          72  tim.o(.bss)
    htim4                                    0x2000032c   Data          72  tim.o(.bss)
    huart4                                   0x20000374   Data          72  usart.o(.bss)
    huart5                                   0x200003bc   Data          72  usart.o(.bss)
    huart1                                   0x20000404   Data          72  usart.o(.bss)
    huart2                                   0x2000044c   Data          72  usart.o(.bss)
    huart6                                   0x20000494   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x200004dc   Data          96  usart.o(.bss)
    hdma_uart5_rx                            0x2000053c   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x2000059c   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x200005fc   Data          96  usart.o(.bss)
    hdma_usart6_rx                           0x2000065c   Data          96  usart.o(.bss)
    uart1_rx_dma_buffer                      0x200006bc   Data         128  uart_driver.o(.bss)
    ring1_buffer_input                       0x2000073c   Data         128  uart_driver.o(.bss)
    ring1_buffer                             0x200007bc   Data          12  uart_driver.o(.bss)
    uart2_rx_dma_buffer                      0x200007c8   Data         128  uart_driver.o(.bss)
    ring2_buffer_input                       0x20000848   Data         128  uart_driver.o(.bss)
    ring2_buffer                             0x200008c8   Data          12  uart_driver.o(.bss)
    uart4_rx_dma_buffer                      0x200008d4   Data         128  uart_driver.o(.bss)
    ring4_buffer_input                       0x20000954   Data         128  uart_driver.o(.bss)
    ring4_buffer                             0x200009d4   Data          12  uart_driver.o(.bss)
    uart5_rx_dma_buffer                      0x200009e0   Data         128  uart_driver.o(.bss)
    ring5_buffer_input                       0x20000a60   Data         128  uart_driver.o(.bss)
    ring5_buffer                             0x20000ae0   Data          12  uart_driver.o(.bss)
    uart6_rx_dma_buffer                      0x20000aec   Data         128  uart_driver.o(.bss)
    ring6_buffer_input                       0x20000b6c   Data         128  uart_driver.o(.bss)
    ring6_buffer                             0x20000bec   Data          12  uart_driver.o(.bss)
    uart1_data_buffer                        0x20000bf8   Data         128  uart_driver.o(.bss)
    uart5_data_buffer                        0x20000c78   Data         128  uart_driver.o(.bss)
    uart6_data_buffer                        0x20000cf8   Data         128  uart_driver.o(.bss)
    pid_x                                    0x20000e28   Data          60  pid_app.o(.bss)
    pid_y                                    0x20000e64   Data          60  pid_app.o(.bss)
    outerRectData                            0x20000ea0   Data          72  uart_app.o(.bss)
    g_pidTargetPoints                        0x20000ee8   Data         196  uart_app.o(.bss)
    Circle_pidTargetPoints                   0x20000fac   Data         164  task_app.o(.bss)
    g_maix_parsed_data                       0x20001050   Data         164  maix_app.o(.bss)
    g_vision_data                            0x200010f4   Data          10  maix_app.o(.bss)
    ff_controller                            0x20001100   Data         100  feedforward_app.o(.bss)
    imu_data                                 0x20001164   Data          40  feedforward_app.o(.bss)
    __heap_base                              0x20001190   Data           0  startup_stm32f407xx.o(HEAP)
    __heap_limit                             0x20001790   Data           0  startup_stm32f407xx.o(HEAP)
    __initial_sp                             0x20002790   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00009658, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x00009508])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000094ac, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         5192  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         5332    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         5335    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5337    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5339    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         5340    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         5347    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         5342    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         5344    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         5333    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO         5195    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x00000024   Code   RO         5197    .text               mc_w.l(memcpya.o)
    0x0800024a   0x0800024a   0x00000024   Code   RO         5199    .text               mc_w.l(memseta.o)
    0x0800026e   0x0800026e   0x0000001e   Code   RO         5203    .text               mc_w.l(strncmp.o)
    0x0800028c   0x0800028c   0x00000038   Code   RO         5268    .text               mc_w.l(__0sscanf.o)
    0x080002c4   0x080002c4   0x0000014c   Code   RO         5270    .text               mc_w.l(_scanf_int.o)
    0x08000410   0x08000410   0x0000014e   Code   RO         5276    .text               mf_w.l(dadd.o)
    0x0800055e   0x0800055e   0x000000e4   Code   RO         5278    .text               mf_w.l(dmul.o)
    0x08000642   0x08000642   0x000000de   Code   RO         5280    .text               mf_w.l(ddiv.o)
    0x08000720   0x08000720   0x00000022   Code   RO         5282    .text               mf_w.l(dflti.o)
    0x08000742   0x08000742   0x0000003e   Code   RO         5284    .text               mf_w.l(dfixi.o)
    0x08000780   0x08000780   0x00000026   Code   RO         5286    .text               mf_w.l(f2d.o)
    0x080007a6   0x080007a6   0x0000002c   Code   RO         5351    .text               mc_w.l(uidiv.o)
    0x080007d2   0x080007d2   0x0000001e   Code   RO         5353    .text               mc_w.l(llshl.o)
    0x080007f0   0x080007f0   0x00000020   Code   RO         5355    .text               mc_w.l(llushr.o)
    0x08000810   0x08000810   0x00000024   Code   RO         5357    .text               mc_w.l(llsshr.o)
    0x08000834   0x08000834   0x0000001c   Code   RO         5368    .text               mc_w.l(_chval.o)
    0x08000850   0x08000850   0x00000028   Code   RO         5370    .text               mc_w.l(scanf_char.o)
    0x08000878   0x08000878   0x00000040   Code   RO         5372    .text               mc_w.l(_sgetc.o)
    0x080008b8   0x080008b8   0x00000000   Code   RO         5376    .text               mc_w.l(iusefp.o)
    0x080008b8   0x080008b8   0x000000ba   Code   RO         5377    .text               mf_w.l(depilogue.o)
    0x08000972   0x08000972   0x00000002   PAD
    0x08000974   0x08000974   0x00000088   Code   RO         5381    .text               mf_w.l(drnd.o)
    0x080009fc   0x080009fc   0x00000030   Code   RO         5383    .text               mf_w.l(dfixul.o)
    0x08000a2c   0x08000a2c   0x00000030   Code   RO         5385    .text               mf_w.l(cdcmple.o)
    0x08000a5c   0x08000a5c   0x00000030   Code   RO         5387    .text               mf_w.l(cdrcmple.o)
    0x08000a8c   0x08000a8c   0x00000004   Code   RO         5389    .text               mf_w.l(fpstat.o)
    0x08000a90   0x08000a90   0x00000024   Code   RO         5401    .text               mc_w.l(init.o)
    0x08000ab4   0x08000ab4   0x00000008   Code   RO         5406    .text               mc_w.l(ctype_o.o)
    0x08000abc   0x08000abc   0x00000012   Code   RO         5428    .text               mc_w.l(isspace_o.o)
    0x08000ace   0x08000ace   0x00000002   PAD
    0x08000ad0   0x08000ad0   0x00000330   Code   RO         5434    .text               mc_w.l(_scanf.o)
    0x08000e00   0x08000e00   0x0000001a   Code   RO         5439    .text               mf_w.l(dfltui.o)
    0x08000e1a   0x08000e1a   0x00000056   Code   RO         5452    .text               mc_w.l(__dczerorl2.o)
    0x08000e70   0x08000e70   0x00000248   Code   RO         3995    i.BNO080_HardwareReset  bno08x_hal.o
    0x080010b8   0x080010b8   0x0000000c   Code   RO         3996    i.BNO080_Init       bno08x_hal.o
    0x080010c4   0x080010c4   0x00000002   Code   RO          484    i.BusFault_Handler  stm32f4xx_it.o
    0x080010c6   0x080010c6   0x00000002   PAD
    0x080010c8   0x080010c8   0x0000000c   Code   RO          485    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x080010d4   0x080010d4   0x0000000c   Code   RO          486    i.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x080010e0   0x080010e0   0x0000000c   Code   RO          487    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x080010ec   0x080010ec   0x0000000c   Code   RO          488    i.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x080010f8   0x080010f8   0x0000000c   Code   RO          489    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08001104   0x08001104   0x00000028   Code   RO         1564    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x0800112c   0x0800112c   0x00000054   Code   RO         1565    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08001180   0x08001180   0x00000028   Code   RO         1566    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080011a8   0x080011a8   0x00000002   Code   RO          490    i.DebugMon_Handler  stm32f4xx_it.o
    0x080011aa   0x080011aa   0x00000038   Code   RO         3878    i.Emm_V5_En_Control  emm_v5.o
    0x080011e2   0x080011e2   0x00000038   Code   RO         3881    i.Emm_V5_Modify_Vel_Scale  emm_v5.o
    0x0800121a   0x0800121a   0x00000032   Code   RO         3885    i.Emm_V5_Origin_Trigger_Return  emm_v5.o
    0x0800124c   0x0800124c   0x00000070   Code   RO         3887    i.Emm_V5_Pos_Control  emm_v5.o
    0x080012bc   0x080012bc   0x0000008e   Code   RO         3888    i.Emm_V5_Read_Sys_Params  emm_v5.o
    0x0800134a   0x0800134a   0x00000030   Code   RO         3890    i.Emm_V5_Reset_CurPos_To_Zero  emm_v5.o
    0x0800137a   0x0800137a   0x00000034   Code   RO         3891    i.Emm_V5_Stop_Now   emm_v5.o
    0x080013ae   0x080013ae   0x00000044   Code   RO         3893    i.Emm_V5_Vel_Control  emm_v5.o
    0x080013f2   0x080013f2   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080013f6   0x080013f6   0x00000092   Code   RO         1567    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08001488   0x08001488   0x00000024   Code   RO         1568    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080014ac   0x080014ac   0x000001a0   Code   RO         1572    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x0800164c   0x0800164c   0x000000d4   Code   RO         1573    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08001720   0x08001720   0x0000006e   Code   RO         1577    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800178e   0x0800178e   0x00000002   PAD
    0x08001790   0x08001790   0x00000024   Code   RO         2004    i.HAL_Delay         stm32f4xx_hal.o
    0x080017b4   0x080017b4   0x000001f0   Code   RO         1460    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x080019a4   0x080019a4   0x0000000a   Code   RO         1462    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x080019ae   0x080019ae   0x0000000a   Code   RO         1464    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080019b8   0x080019b8   0x0000000c   Code   RO         2010    i.HAL_GetTick       stm32f4xx_hal.o
    0x080019c4   0x080019c4   0x00000188   Code   RO          661    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x08001b4c   0x08001b4c   0x000001f0   Code   RO          667    i.HAL_I2C_Master_Receive  stm32f4xx_hal_i2c.o
    0x08001d3c   0x08001d3c   0x0000012c   Code   RO          674    i.HAL_I2C_Master_Transmit  stm32f4xx_hal_i2c.o
    0x08001e68   0x08001e68   0x000000ac   Code   RO          293    i.HAL_I2C_MspInit   i2c.o
    0x08001f14   0x08001f14   0x00000010   Code   RO         2016    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001f24   0x08001f24   0x00000034   Code   RO         2017    i.HAL_Init          stm32f4xx_hal.o
    0x08001f58   0x08001f58   0x00000040   Code   RO         2018    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001f98   0x08001f98   0x00000030   Code   RO          626    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001fc8   0x08001fc8   0x0000001a   Code   RO         1852    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001fe2   0x08001fe2   0x00000002   PAD
    0x08001fe4   0x08001fe4   0x00000040   Code   RO         1858    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002024   0x08002024   0x00000024   Code   RO         1859    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002048   0x08002048   0x00000134   Code   RO         1106    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x0800217c   0x0800217c   0x00000020   Code   RO         1113    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800219c   0x0800219c   0x00000020   Code   RO         1114    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x080021bc   0x080021bc   0x00000060   Code   RO         1115    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800221c   0x0800221c   0x0000036c   Code   RO         1118    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08002588   0x08002588   0x00000028   Code   RO         1863    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080025b0   0x080025b0   0x00000002   Code   RO         2961    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x080025b2   0x080025b2   0x00000002   Code   RO         2962    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x080025b4   0x080025b4   0x00000054   Code   RO         2964    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08002608   0x08002608   0x00000090   Code   RO         2980    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08002698   0x08002698   0x0000005a   Code   RO         2257    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x080026f2   0x080026f2   0x00000002   PAD
    0x080026f4   0x080026f4   0x00000054   Code   RO          341    i.HAL_TIM_Base_MspInit  tim.o
    0x08002748   0x08002748   0x00000080   Code   RO         2262    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x080027c8   0x080027c8   0x000000dc   Code   RO         2266    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x080028a4   0x080028a4   0x000000a4   Code   RO         2278    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08002948   0x08002948   0x000000a4   Code   RO          343    i.HAL_TIM_Encoder_MspInit  tim.o
    0x080029ec   0x080029ec   0x00000002   Code   RO         2291    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x080029ee   0x080029ee   0x00000130   Code   RO         2305    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08002b1e   0x08002b1e   0x00000002   PAD
    0x08002b20   0x08002b20   0x00000054   Code   RO          344    i.HAL_TIM_MspPostInit  tim.o
    0x08002b74   0x08002b74   0x00000002   Code   RO         2308    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08002b76   0x08002b76   0x000000cc   Code   RO         2329    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002c42   0x08002c42   0x0000005a   Code   RO         2332    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08002c9c   0x08002c9c   0x00000002   Code   RO         2334    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08002c9e   0x08002c9e   0x00000002   Code   RO         2335    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08002ca0   0x08002ca0   0x00000060   Code   RO         4408    i.HAL_TIM_PeriodElapsedCallback  scheduler.o
    0x08002d00   0x08002d00   0x00000002   Code   RO         2348    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08002d02   0x08002d02   0x0000004a   Code   RO         3238    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08002d4c   0x08002d4c   0x00000170   Code   RO         3824    i.HAL_UARTEx_RxEventCallback  uart_driver.o
    0x08002ebc   0x08002ebc   0x00000070   Code   RO         3252    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08002f2c   0x08002f2c   0x00000002   Code   RO         3254    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08002f2e   0x08002f2e   0x00000002   PAD
    0x08002f30   0x08002f30   0x00000280   Code   RO         3257    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080031b0   0x080031b0   0x00000064   Code   RO         3258    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08003214   0x08003214   0x00000300   Code   RO          419    i.HAL_UART_MspInit  usart.o
    0x08003514   0x08003514   0x00000002   Code   RO         3264    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08003516   0x08003516   0x00000002   Code   RO         3265    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08003518   0x08003518   0x000000a0   Code   RO         3266    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080035b8   0x080035b8   0x00000002   Code   RO         3269    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080035ba   0x080035ba   0x00000002   Code   RO          491    i.HardFault_Handler  stm32f4xx_it.o
    0x080035bc   0x080035bc   0x0000002e   Code   RO          704    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x080035ea   0x080035ea   0x00000002   PAD
    0x080035ec   0x080035ec   0x000000ec   Code   RO          707    i.I2C_MasterRequestRead  stm32f4xx_hal_i2c.o
    0x080036d8   0x080036d8   0x0000009c   Code   RO          708    i.I2C_MasterRequestWrite  stm32f4xx_hal_i2c.o
    0x08003774   0x08003774   0x00000056   Code   RO          719    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080037ca   0x080037ca   0x00000002   PAD
    0x080037cc   0x080037cc   0x00000090   Code   RO          720    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800385c   0x0800385c   0x000000bc   Code   RO          721    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003918   0x08003918   0x00000070   Code   RO          722    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003988   0x08003988   0x00000056   Code   RO          723    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080039de   0x080039de   0x00000002   Code   RO         4493    i.Led_Init          led_app.o
    0x080039e0   0x080039e0   0x0000007c   Code   RO          268    i.MX_DMA_Init       dma.o
    0x08003a5c   0x08003a5c   0x0000015c   Code   RO          244    i.MX_GPIO_Init      gpio.o
    0x08003bb8   0x08003bb8   0x00000040   Code   RO          294    i.MX_I2C1_Init      i2c.o
    0x08003bf8   0x08003bf8   0x00000040   Code   RO          295    i.MX_I2C2_Init      i2c.o
    0x08003c38   0x08003c38   0x000000d8   Code   RO          345    i.MX_TIM1_Init      tim.o
    0x08003d10   0x08003d10   0x00000068   Code   RO          346    i.MX_TIM2_Init      tim.o
    0x08003d78   0x08003d78   0x0000006c   Code   RO          347    i.MX_TIM3_Init      tim.o
    0x08003de4   0x08003de4   0x0000006c   Code   RO          348    i.MX_TIM4_Init      tim.o
    0x08003e50   0x08003e50   0x00000038   Code   RO          420    i.MX_UART4_Init     usart.o
    0x08003e88   0x08003e88   0x00000038   Code   RO          421    i.MX_UART5_Init     usart.o
    0x08003ec0   0x08003ec0   0x00000038   Code   RO          422    i.MX_USART1_UART_Init  usart.o
    0x08003ef8   0x08003ef8   0x00000038   Code   RO          423    i.MX_USART2_UART_Init  usart.o
    0x08003f30   0x08003f30   0x00000038   Code   RO          424    i.MX_USART6_UART_Init  usart.o
    0x08003f68   0x08003f68   0x00000002   Code   RO          492    i.MemManage_Handler  stm32f4xx_it.o
    0x08003f6a   0x08003f6a   0x00000002   Code   RO          493    i.NMI_Handler       stm32f4xx_it.o
    0x08003f6c   0x08003f6c   0x00000002   Code   RO          494    i.PendSV_Handler    stm32f4xx_it.o
    0x08003f6e   0x08003f6e   0x00000002   PAD
    0x08003f70   0x08003f70   0x00000194   Code   RO         3997    i.QuaternionToEulerAngles  bno08x_hal.o
    0x08004104   0x08004104   0x00000002   Code   RO          495    i.SVC_Handler       stm32f4xx_it.o
    0x08004106   0x08004106   0x00000002   PAD
    0x08004108   0x08004108   0x00000014   Code   RO         4409    i.Scheduler_Init    scheduler.o
    0x0800411c   0x0800411c   0x0000003c   Code   RO         4410    i.Scheduler_Run     scheduler.o
    0x08004158   0x08004158   0x00000044   Code   RO         4782    i.Step_Motor_Init   servo_app.o
    0x0800419c   0x0800419c   0x00000024   Code   RO         4783    i.Step_Motor_Return  servo_app.o
    0x080041c0   0x080041c0   0x000000cc   Code   RO         4786    i.Step_Motor_Set_Speed_my  servo_app.o
    0x0800428c   0x0800428c   0x00000024   Code   RO         4787    i.Step_Motor_Stop   servo_app.o
    0x080042b0   0x080042b0   0x00000004   Code   RO          496    i.SysTick_Handler   stm32f4xx_it.o
    0x080042b4   0x080042b4   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x08004348   0x08004348   0x00000010   Code   RO         3592    i.SystemInit        system_stm32f4xx.o
    0x08004358   0x08004358   0x0000009c   Code   RO         4411    i.System_Init       scheduler.o
    0x080043f4   0x080043f4   0x0000000c   Code   RO          497    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08004400   0x08004400   0x000000d0   Code   RO         2350    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x080044d0   0x080044d0   0x00000014   Code   RO         2361    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x080044e4   0x080044e4   0x00000010   Code   RO         2362    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x080044f4   0x080044f4   0x00000060   Code   RO         2363    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08004554   0x08004554   0x0000006c   Code   RO         2364    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x080045c0   0x080045c0   0x00000068   Code   RO         2365    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08004628   0x08004628   0x00000050   Code   RO         2366    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08004678   0x08004678   0x00000022   Code   RO         2368    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x0800469a   0x0800469a   0x00000024   Code   RO         2370    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080046be   0x080046be   0x00000002   PAD
    0x080046c0   0x080046c0   0x0000000c   Code   RO          498    i.UART4_IRQHandler  stm32f4xx_it.o
    0x080046cc   0x080046cc   0x0000000c   Code   RO          499    i.UART5_IRQHandler  stm32f4xx_it.o
    0x080046d8   0x080046d8   0x0000000e   Code   RO         3271    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080046e6   0x080046e6   0x0000004a   Code   RO         3272    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08004730   0x08004730   0x00000086   Code   RO         3273    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x080047b6   0x080047b6   0x0000001e   Code   RO         3275    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x080047d4   0x080047d4   0x0000004e   Code   RO         3281    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08004822   0x08004822   0x0000001c   Code   RO         3282    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x0800483e   0x0800483e   0x000000c2   Code   RO         3283    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08004900   0x08004900   0x0000010c   Code   RO         3284    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08004a0c   0x08004a0c   0x000000a0   Code   RO         3285    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08004aac   0x08004aac   0x00000072   Code   RO         3287    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08004b1e   0x08004b1e   0x00000002   PAD
    0x08004b20   0x08004b20   0x0000000c   Code   RO          500    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08004b2c   0x08004b2c   0x0000000c   Code   RO          501    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08004b38   0x08004b38   0x0000000c   Code   RO          502    i.USART6_IRQHandler  stm32f4xx_it.o
    0x08004b44   0x08004b44   0x0000005c   Code   RO         4678    i.Uart1_Task        uart_app.o
    0x08004ba0   0x08004ba0   0x00000048   Code   RO         4681    i.Uart5_Task        uart_app.o
    0x08004be8   0x08004be8   0x000000f4   Code   RO         3825    i.Uart_Init         uart_driver.o
    0x08004cdc   0x08004cdc   0x00000002   Code   RO          503    i.UsageFault_Handler  stm32f4xx_it.o
    0x08004cde   0x08004cde   0x00000002   PAD
    0x08004ce0   0x08004ce0   0x00000034   Code   RO         5218    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08004d14   0x08004d14   0x00000030   Code   RO         5397    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08004d44   0x08004d44   0x00000026   Code   RO         5305    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08004d6a   0x08004d6a   0x00000012   Code   RO         3998    i.__ARM_isinff      bno08x_hal.o
    0x08004d7c   0x08004d7c   0x0000000c   Code   RO         3999    i.__ARM_isnanf      bno08x_hal.o
    0x08004d88   0x08004d88   0x00000020   Code   RO         1865    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08004da8   0x08004da8   0x0000012c   Code   RO         5114    i.__hardfp_asinf    m_wm.l(asinf.o)
    0x08004ed4   0x08004ed4   0x000002ac   Code   RO         5126    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x08005180   0x08005180   0x000000c8   Code   RO         5138    i.__hardfp_cos      m_wm.l(cos.o)
    0x08005248   0x08005248   0x00000664   Code   RO         5150    i.__hardfp_powf     m_wm.l(powf.o)
    0x080058ac   0x080058ac   0x00000004   PAD
    0x080058b0   0x080058b0   0x000000e0   Code   RO         5164    i.__hardfp_round    m_wm.l(round.o)
    0x08005990   0x08005990   0x000000c8   Code   RO         5168    i.__hardfp_sin      m_wm.l(sin.o)
    0x08005a58   0x08005a58   0x0000003a   Code   RO         5180    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08005a92   0x08005a92   0x00000006   PAD
    0x08005a98   0x08005a98   0x00000438   Code   RO         5321    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x08005ed0   0x08005ed0   0x00000170   Code   RO         5288    i.__kernel_cos      m_wm.l(cos_i.o)
    0x08006040   0x08006040   0x000000f8   Code   RO         5399    i.__kernel_poly     m_wm.l(poly.o)
    0x08006138   0x08006138   0x00000130   Code   RO         5326    i.__kernel_sin      m_wm.l(sin_i.o)
    0x08006268   0x08006268   0x00000014   Code   RO         5292    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x0800627c   0x0800627c   0x00000004   PAD
    0x08006280   0x08006280   0x00000020   Code   RO         5294    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x080062a0   0x080062a0   0x00000020   Code   RO         5297    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x080062c0   0x080062c0   0x00000014   Code   RO         5307    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x080062d4   0x080062d4   0x00000006   Code   RO         5308    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x080062da   0x080062da   0x00000006   Code   RO         5309    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x080062e0   0x080062e0   0x00000010   Code   RO         5310    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x080062f0   0x080062f0   0x00000010   Code   RO         5311    i.__mathlib_flt_overflow  m_wm.l(funder.o)
    0x08006300   0x08006300   0x00000010   Code   RO         5313    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08006310   0x08006310   0x0000000e   Code   RO         5446    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800631e   0x0800631e   0x00000002   Code   RO         5447    i.__scatterload_null  mc_w.l(handlers.o)
    0x08006320   0x08006320   0x0000000e   Code   RO         5448    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800632e   0x0800632e   0x00000002   PAD
    0x08006330   0x08006330   0x0000000c   Code   RO         5363    i.__set_errno       mc_w.l(errno.o)
    0x0800633c   0x0800633c   0x00000184   Code   RO         5220    i._fp_digits        mc_w.l(printfa.o)
    0x080064c0   0x080064c0   0x000006b4   Code   RO         5221    i._printf_core      mc_w.l(printfa.o)
    0x08006b74   0x08006b74   0x00000024   Code   RO         5222    i._printf_post_padding  mc_w.l(printfa.o)
    0x08006b98   0x08006b98   0x0000002e   Code   RO         5223    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08006bc6   0x08006bc6   0x00000016   Code   RO         5224    i._snputc           mc_w.l(printfa.o)
    0x08006bdc   0x08006bdc   0x0000016c   Code   RO         4566    i.app_pid_calc      pid_app.o
    0x08006d48   0x08006d48   0x00000048   Code   RO         4567    i.app_pid_init      pid_app.o
    0x08006d90   0x08006d90   0x00000418   Code   RO         4568    i.app_pid_parse_cmd  pid_app.o
    0x080071a8   0x080071a8   0x00000034   Code   RO         4570    i.app_pid_set_target  pid_app.o
    0x080071dc   0x080071dc   0x00000034   Code   RO         4571    i.app_pid_set_x_params  pid_app.o
    0x08007210   0x08007210   0x00000034   Code   RO         4572    i.app_pid_set_y_params  pid_app.o
    0x08007244   0x08007244   0x00000048   Code   RO         4573    i.app_pid_start     pid_app.o
    0x0800728c   0x0800728c   0x00000034   Code   RO         4574    i.app_pid_stop      pid_app.o
    0x080072c0   0x080072c0   0x00000014   Code   RO         4575    i.app_pid_task      pid_app.o
    0x080072d4   0x080072d4   0x0000003c   Code   RO         4576    i.app_pid_update_position  pid_app.o
    0x08007310   0x08007310   0x00000134   Code   RO         4957    i.bno080_task       bno08x_app.o
    0x08007444   0x08007444   0x000003e4   Code   RO         4684    i.calculate_and_store_perimeter_points  uart_app.o
    0x08007828   0x08007828   0x00000060   Code   RO         4958    i.convert_to_continuous_yaw  bno08x_app.o
    0x08007888   0x08007888   0x00000030   Code   RO         4005    i.dataAvailable     bno08x_hal.o
    0x080078b8   0x080078b8   0x0000000a   Code   RO         4006    i.enableAccelerometer  bno08x_hal.o
    0x080078c2   0x080078c2   0x0000000a   Code   RO         4008    i.enableGyro        bno08x_hal.o
    0x080078cc   0x080078cc   0x0000000a   Code   RO         4011    i.enableRotationVector  bno08x_hal.o
    0x080078d6   0x080078d6   0x00000018   Code   RO         5393    i.fabs              m_wm.l(fabs.o)
    0x080078ee   0x080078ee   0x00000002   PAD
    0x080078f0   0x080078f0   0x000000f8   Code   RO         5018    i.feedforward_calculate_compensation  feedforward_app.o
    0x080079e8   0x080079e8   0x000001dc   Code   RO         5019    i.feedforward_calibrate  feedforward_app.o
    0x08007bc4   0x08007bc4   0x00000020   Code   RO         5020    i.feedforward_get_compensation  feedforward_app.o
    0x08007be4   0x08007be4   0x0000005c   Code   RO         5021    i.feedforward_identify_motion_state  feedforward_app.o
    0x08007c40   0x08007c40   0x00000088   Code   RO         5022    i.feedforward_init  feedforward_app.o
    0x08007cc8   0x08007cc8   0x0000005c   Code   RO         5023    i.feedforward_print_compensation  feedforward_app.o
    0x08007d24   0x08007d24   0x00000064   Code   RO         5024    i.feedforward_print_status  feedforward_app.o
    0x08007d88   0x08007d88   0x0000010c   Code   RO         5025    i.feedforward_process  feedforward_app.o
    0x08007e94   0x08007e94   0x0000000c   Code   RO         5026    i.feedforward_set_parameters  feedforward_app.o
    0x08007ea0   0x08007ea0   0x00000004   Code   RO         5028    i.feedforward_task  feedforward_app.o
    0x08007ea4   0x08007ea4   0x0000006c   Code   RO         5029    i.feedforward_update_imu_data  feedforward_app.o
    0x08007f10   0x08007f10   0x00000050   Code   RO         5240    i.free              mc_w.l(malloc.o)
    0x08007f60   0x08007f60   0x000000e8   Code   RO         4839    i.generateCirclePoints  task_app.o
    0x08008048   0x08008048   0x00000014   Code   RO         4017    i.getAccelX         bno08x_hal.o
    0x0800805c   0x0800805c   0x00000014   Code   RO         4018    i.getAccelY         bno08x_hal.o
    0x08008070   0x08008070   0x00000014   Code   RO         4019    i.getAccelZ         bno08x_hal.o
    0x08008084   0x08008084   0x00000014   Code   RO         4022    i.getGyroX          bno08x_hal.o
    0x08008098   0x08008098   0x00000014   Code   RO         4023    i.getGyroY          bno08x_hal.o
    0x080080ac   0x080080ac   0x00000014   Code   RO         4024    i.getGyroZ          bno08x_hal.o
    0x080080c0   0x080080c0   0x00000014   Code   RO         4037    i.getQuatI          bno08x_hal.o
    0x080080d4   0x080080d4   0x00000014   Code   RO         4038    i.getQuatJ          bno08x_hal.o
    0x080080e8   0x080080e8   0x00000014   Code   RO         4039    i.getQuatK          bno08x_hal.o
    0x080080fc   0x080080fc   0x00000014   Code   RO         4041    i.getQuatReal       bno08x_hal.o
    0x08008110   0x08008110   0x0000000c   Code   RO         4959    i.get_pitch         bno08x_app.o
    0x0800811c   0x0800811c   0x0000000c   Code   RO         4960    i.get_roll          bno08x_app.o
    0x08008128   0x08008128   0x00000010   Code   RO         4961    i.get_yaw           bno08x_app.o
    0x08008138   0x08008138   0x00000018   Code   RO         4896    i.init_vision_data  maix_app.o
    0x08008150   0x08008150   0x00000040   Code   RO         4532    i.key_read          key_app.o
    0x08008190   0x08008190   0x00000048   Code   RO         4533    i.key_task          key_app.o
    0x080081d8   0x080081d8   0x00000046   Code   RO           15    i.main              main.o
    0x0800821e   0x0800821e   0x00000002   PAD
    0x08008220   0x08008220   0x000000a8   Code   RO         4897    i.maixcam_task      maix_app.o
    0x080082c8   0x080082c8   0x0000006c   Code   RO         5241    i.malloc            mc_w.l(malloc.o)
    0x08008334   0x08008334   0x00000110   Code   RO         4962    i.my_bno080_init    bno08x_app.o
    0x08008444   0x08008444   0x00000032   Code   RO         3826    i.my_printf         uart_driver.o
    0x08008476   0x08008476   0x00000002   PAD
    0x08008478   0x08008478   0x000000d8   Code   RO         4046    i.parseInputReport  bno08x_hal.o
    0x08008550   0x08008550   0x000000b4   Code   RO         4898    i.parse_uart_message  maix_app.o
    0x08008604   0x08008604   0x00000024   Code   RO         3657    i.pid_app_limit_integral  pid.o
    0x08008628   0x08008628   0x00000066   Code   RO         3659    i.pid_calculate_positional  pid.o
    0x0800868e   0x0800868e   0x00000002   PAD
    0x08008690   0x08008690   0x000000f4   Code   RO         4577    i.pid_circle        pid_app.o
    0x08008784   0x08008784   0x00000020   Code   RO         3660    i.pid_constrain     pid.o
    0x080087a4   0x080087a4   0x00000084   Code   RO         4578    i.pid_follow        pid_app.o
    0x08008828   0x08008828   0x00000030   Code   RO         3661    i.pid_init          pid.o
    0x08008858   0x08008858   0x000000f4   Code   RO         4579    i.pid_ju            pid_app.o
    0x0800894c   0x0800894c   0x00000090   Code   RO         4580    i.pid_only_target   pid_app.o
    0x080089dc   0x080089dc   0x00000026   Code   RO         3662    i.pid_out_limit     pid.o
    0x08008a02   0x08008a02   0x00000002   PAD
    0x08008a04   0x08008a04   0x00000028   Code   RO         3663    i.pid_reset         pid.o
    0x08008a2c   0x08008a2c   0x00000006   Code   RO         3664    i.pid_set_limit     pid.o
    0x08008a32   0x08008a32   0x00000006   Code   RO         3665    i.pid_set_params    pid.o
    0x08008a38   0x08008a38   0x00000006   Code   RO         3666    i.pid_set_target    pid.o
    0x08008a3e   0x08008a3e   0x00000002   PAD
    0x08008a40   0x08008a40   0x00000244   Code   RO         4688    i.process_command   uart_app.o
    0x08008c84   0x08008c84   0x000000a0   Code   RO         4689    i.process_reset_command  uart_app.o
    0x08008d24   0x08008d24   0x0000002a   Code   RO         4047    i.qToFloat          bno08x_hal.o
    0x08008d4e   0x08008d4e   0x00000002   PAD
    0x08008d50   0x08008d50   0x000000d8   Code   RO         4050    i.receivePacket     bno08x_hal.o
    0x08008e28   0x08008e28   0x00000030   Code   RO         3738    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08008e58   0x08008e58   0x0000006e   Code   RO         3739    i.rt_ringbuffer_get  ringbuffer.o
    0x08008ec6   0x08008ec6   0x00000026   Code   RO         3741    i.rt_ringbuffer_init  ringbuffer.o
    0x08008eec   0x08008eec   0x00000072   Code   RO         3743    i.rt_ringbuffer_put  ringbuffer.o
    0x08008f5e   0x08008f5e   0x00000020   Code   RO         3748    i.rt_ringbuffer_status  ringbuffer.o
    0x08008f7e   0x08008f7e   0x00000002   PAD
    0x08008f80   0x08008f80   0x00000060   Code   RO         4690    i.save_initial_position  uart_app.o
    0x08008fe0   0x08008fe0   0x00000070   Code   RO         4055    i.sendPacket        bno08x_hal.o
    0x08009050   0x08009050   0x0000004c   Code   RO         4056    i.setFeatureCommand  bno08x_hal.o
    0x0800909c   0x0800909c   0x00000034   Code   RO         4057    i.softReset         bno08x_hal.o
    0x080090d0   0x080090d0   0x0000003e   Code   RO         5182    i.sqrtf             m_wm.l(sqrtf.o)
    0x0800910e   0x0800910e   0x00000008   Data   RO         1579    .constdata          stm32f4xx_hal_dma.o
    0x08009116   0x08009116   0x00000010   Data   RO         3593    .constdata          system_stm32f4xx.o
    0x08009126   0x08009126   0x00000008   Data   RO         3594    .constdata          system_stm32f4xx.o
    0x0800912e   0x0800912e   0x00000002   PAD
    0x08009130   0x08009130   0x00000010   Data   RO         5031    .constdata          feedforward_app.o
    0x08009140   0x08009140   0x00000140   Data   RO         5153    .constdata          m_wm.l(powf.o)
    0x08009280   0x08009280   0x00000030   Data   RO         5289    .constdata          m_wm.l(cos_i.o)
    0x080092b0   0x080092b0   0x000000c8   Data   RO         5323    .constdata          m_wm.l(rred.o)
    0x08009378   0x08009378   0x00000028   Data   RO         5327    .constdata          m_wm.l(sin_i.o)
    0x080093a0   0x080093a0   0x00000081   Data   RO         5407    .constdata          mc_w.l(ctype_o.o)
    0x08009421   0x08009421   0x00000003   PAD
    0x08009424   0x08009424   0x00000004   Data   RO         5408    .constdata          mc_w.l(ctype_o.o)
    0x08009428   0x08009428   0x00000042   Data   RO         4692    .conststring        uart_app.o
    0x0800946a   0x0800946a   0x00000002   PAD
    0x0800946c   0x0800946c   0x0000001d   Data   RO         5032    .conststring        feedforward_app.o
    0x08009489   0x08009489   0x00000003   PAD
    0x0800948c   0x0800948c   0x00000020   Data   RO         5444    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080094ac, Size: 0x00002790, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x0000005c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000000c   Data   RW         2024    .data               stm32f4xx_hal.o
    0x2000000c   COMPRESSED   0x00000004   Data   RW         3595    .data               system_stm32f4xx.o
    0x20000010   COMPRESSED   0x0000005a   Data   RW         4060    .data               bno08x_hal.o
    0x2000006a   COMPRESSED   0x00000002   PAD
    0x2000006c   COMPRESSED   0x0000001c   Data   RW         4412    .data               scheduler.o
    0x20000088   COMPRESSED   0x00000001   Data   RW         4534    .data               key_app.o
    0x20000089   COMPRESSED   0x00000004   Data   RW         4535    .data               key_app.o
    0x2000008d   COMPRESSED   0x00000003   PAD
    0x20000090   COMPRESSED   0x00000054   Data   RW         4582    .data               pid_app.o
    0x200000e4   COMPRESSED   0x0000002c   Data   RW         4693    .data               uart_app.o
    0x20000110   COMPRESSED   0x00000001   Data   RW         4788    .data               servo_app.o
    0x20000111   COMPRESSED   0x00000003   PAD
    0x20000114   COMPRESSED   0x00000068   Data   RW         4844    .data               task_app.o
    0x2000017c   COMPRESSED   0x00000020   Data   RW         4963    .data               bno08x_app.o
    0x2000019c   COMPRESSED   0x00000004   Data   RW         5033    .data               feedforward_app.o
    0x200001a0   COMPRESSED   0x00000004   Data   RW         5359    .data               mc_w.l(mvars.o)
    0x200001a4   COMPRESSED   0x00000004   Data   RW         5360    .data               mc_w.l(mvars.o)
    0x200001a8   COMPRESSED   0x00000004   Data   RW         5364    .data               mc_w.l(errno.o)
    0x200001ac        -       0x000000a8   Zero   RW          296    .bss                i2c.o
    0x20000254        -       0x00000120   Zero   RW          349    .bss                tim.o
    0x20000374        -       0x00000348   Zero   RW          425    .bss                usart.o
    0x200006bc        -       0x0000053c   Zero   RW         3827    .bss                uart_driver.o
    0x20000bf8        -       0x00000080   Zero   RW         3828    .bss                uart_driver.o
    0x20000c78        -       0x00000080   Zero   RW         3831    .bss                uart_driver.o
    0x20000cf8        -       0x00000080   Zero   RW         3832    .bss                uart_driver.o
    0x20000d78        -       0x000000a4   Zero   RW         4058    .bss                bno08x_hal.o
    0x20000e1c        -       0x0000000a   Zero   RW         4059    .bss                bno08x_hal.o
    0x20000e26   COMPRESSED   0x00000002   PAD
    0x20000e28        -       0x00000078   Zero   RW         4581    .bss                pid_app.o
    0x20000ea0        -       0x0000010c   Zero   RW         4691    .bss                uart_app.o
    0x20000fac        -       0x000000a4   Zero   RW         4843    .bss                task_app.o
    0x20001050        -       0x000000ae   Zero   RW         4899    .bss                maix_app.o
    0x200010fe   COMPRESSED   0x00000002   PAD
    0x20001100        -       0x0000008c   Zero   RW         5030    .bss                feedforward_app.o
    0x2000118c   COMPRESSED   0x00000004   PAD
    0x20001190        -       0x00000600   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20001790        -       0x00001000   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08009508, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       716        292          0         32          0       4157   bno08x_app.o
      2022        428          0         90        174      22512   bno08x_hal.o
       124          4          0          0          0        846   dma.o
       584         18          0          0          0       7036   emm_v5.o
      1568        384         45          4        140       8500   feedforward_app.o
       348         20          0          0          0       1119   gpio.o
       300         46          0          0        168       2378   i2c.o
       136         14          0          5          0       1418   key_app.o
         2          0          0          0          0        427   led_app.o
       222         10          0          0          0     705893   main.o
       372         70          0          0        174       3982   maix_app.o
       314         12          0          0          0       6321   pid.o
      2608        690          0         84        120       9768   pid_app.o
       342          0          0          0          0       6230   ringbuffer.o
       332         86          0         28          0       4514   scheduler.o
       344         54          0          1          0       2872   servo_app.o
        36          8        392          0       5632        836   startup_stm32f407xx.o
       180         28          0         12          0       9421   stm32f4xx_hal.o
       198         14          0          0          0      33847   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7366   stm32f4xx_hal_dma.o
       516         46          0          0          0       2863   stm32f4xx_hal_gpio.o
      2242         52          0          0          0      12838   stm32f4xx_hal_i2c.o
        48          6          0          0          0        862   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5272   stm32f4xx_hal_rcc.o
      1912        108          0          0          0      16497   stm32f4xx_hal_tim.o
       232         28          0          0          0       3321   stm32f4xx_hal_tim_ex.o
      2188         28          0          0          0      15752   stm32f4xx_hal_uart.o
       152         66          0          0          0       9404   stm32f4xx_it.o
        16          4         24          4          0       1135   system_stm32f4xx.o
       232         18          0        104        164       2670   task_app.o
       868         78          0          0        288       5297   tim.o
      1996        532         66         44        268       6551   uart_app.o
       662        140          0          0       1724       4397   uart_driver.o
      1048         92          0          0        840       5119   usart.o

    ----------------------------------------------------------------------
     25328       <USER>        <GROUP>        416       9700     931421   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          7          8          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       300         42          0          0          0        176   asinf.o
       684         90          0          0          0        208   atan2f.o
       200         20          0          0          0        164   cos.o
       368         46         48          0          0        200   cos_i.o
        84         16          0          0          0        372   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
        80         24          0          0          0        696   funder.o
       248          0          0          0          0        152   poly.o
      1636        110        320          0          0        372   powf.o
       224         30          0          0          0        172   round.o
      1080        142        200          0          0        188   rred.o
       200         20          0          0          0        164   sin.o
       304         24         40          0          0        208   sin_i.o
       120          0          0          0          0        272   sqrtf.o
        56          8          0          0          0         84   __0sscanf.o
        86          0          0          0          0          0   __dczerorl2.o
        28          0          0          0          0         68   _chval.o
       816          6          0          0          0        112   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        18          0          0          0          0         76   isspace_o.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       188         20          0          0          0        160   malloc.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
         0          0          0          8          0          0   mvars.o
      2260         86          0          0          0        528   printfa.o
        40          8          0          0          0         84   scanf_char.o
        30          0          0          0          0         80   strncmp.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
       136          4          0          0          0         92   drnd.o
        38          0          0          0          0         68   f2d.o
         4          0          0          0          0         68   fpstat.o

    ----------------------------------------------------------------------
     11414        <USER>        <GROUP>         12          0       7020   Library Totals
        22          4          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5638        564        608          0          0       3708   m_wm.l
      4340        154        133         12          0       2128   mc_w.l
      1414          4          0          0          0       1184   mf_w.l

    ----------------------------------------------------------------------
     11414        <USER>        <GROUP>         12          0       7020   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     36742       4190       1318        428       9700     913457   Grand Totals
     36742       4190       1318         92       9700     913457   ELF Image Totals (compressed)
     36742       4190       1318         92          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                38060 (  37.17kB)
    Total RW  Size (RW Data + ZI Data)             10128 (   9.89kB)
    Total ROM Size (Code + RO Data + RW Data)      38152 (  37.26kB)

==============================================================================

