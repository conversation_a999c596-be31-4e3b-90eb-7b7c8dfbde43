# PID+前馈控制系统使用指南

## 系统概述

本系统实现了PID控制与前馈控制的融合，用于云台激光追踪系统。前馈控制基于IMU传感器数据，预测小车运动对云台的影响，提前进行补偿，显著提高系统的动态响应性能。

## 核心原理

### 传统PID控制问题
- **滞后性**：小车转弯 → 云台被甩动 → 激光偏离 → PID检测误差 → 开始纠正
- **抖动**：在这段延迟时间内，激光点会产生明显抖动

### 前馈控制优势
- **预测性**：小车转弯 → IMU检测转动 → 立即补偿 → 激光保持稳定
- **快速响应**：几乎没有延迟，大幅减少激光点抖动

## 系统架构

```
MaixCam激光数据 → PID控制器 ↘
                              → 电机控制 → 云台运动
BNO08X IMU数据 → 前馈控制器 ↗
```

### 数据流程
1. **视觉数据**：MaixCam提供激光点坐标，用于PID控制
2. **IMU数据**：BNO08X提供加速度和角速度，用于前馈补偿
3. **融合控制**：PID输出 + 前馈补偿 = 最终电机控制信号

## 硬件配置

### IMU传感器 (BNO08X)
- **接口**：I2C (地址: 0x4B)
- **数据频率**：50ms (加速度计 + 陀螺仪)
- **安装要求**：与小车刚性连接，避免振动

### 云台电机
- **X轴**：对应偏航控制 (Yaw)
- **Y轴**：对应俯仰控制 (Pitch)
- **通信**：UART (X轴-UART2, Y轴-UART4)

## 软件配置

### 初始化流程
1. **系统启动**：`System_Init()` 中自动初始化
2. **IMU校准**：系统启动后自动进行偏置校准
3. **前馈启用**：与PID系统同步运行

### 关键参数

#### 前馈补偿系数
- **K_pitch**：俯仰补偿系数 (默认: 0.8)
  - 物理含义：每1m/s²加速度需要补偿的角度
  - 调试：太小则加速时激光上移，太大则下移
  
- **K_yaw**：偏航补偿系数 (默认: 1.2)
  - 物理含义：转弯时的角度补偿系数
  - 调试：太小则转弯时激光外偏，太大则内偏
  
- **K_centrifugal**：离心力补偿系数 (默认: 0.5)
  - 物理含义：高速转弯时的离心力补偿
  - 调试：根据实际转弯效果调整

#### 物理参数
- **gimbal_height**：云台高度 (默认: 0.15m)
- **filter_alpha**：滤波系数 (默认: 0.2)

#### 检测阈值
- **turn_threshold**：转弯检测阈值 (默认: 0.3 rad/s)
- **accel_threshold**：加速度检测阈值 (默认: 0.5 m/s²)

## 串口调试命令

### 前馈控制命令

#### 1. 校准命令
```
FF_CAL
```
- **功能**：重新校准IMU偏置
- **使用**：设备静止时发送，校准过程约5秒

#### 2. 参数设置
```
FF:80,120,50
```
- **格式**：FF:K_pitch,K_yaw,K_centrifugal (放大100倍)
- **示例**：FF:80,120,50 表示 K_pitch=0.8, K_yaw=1.2, K_centrifugal=0.5

#### 3. 状态查询
```
FF_STATUS
```
- **功能**：显示前馈系统状态和当前补偿值

### PID控制命令 (保持不变)
```
START          # 启动PID控制
STOP           # 停止PID控制
PX:300,60,15   # 设置X轴PID参数
PY:280,55,12   # 设置Y轴PID参数
R: X=50, Y=150 # 设置目标位置
```

## 调试流程

### 1. 基础验证
1. **检查IMU数据**：观察串口输出的姿态角数据
2. **校准确认**：发送`FF_CAL`命令，确认校准完成
3. **状态检查**：发送`FF_STATUS`查看系统状态

### 2. 参数调试

#### 俯仰补偿调试
1. **测试条件**：小车直线加速/减速
2. **观察现象**：激光点上下移动
3. **调整方法**：
   - 激光点上移 → 增大K_pitch
   - 激光点下移 → 减小K_pitch
   - 目标：加速时激光点保持稳定

#### 偏航补偿调试
1. **测试条件**：小车原地转圈或固定速度转弯
2. **观察现象**：激光点左右偏移
3. **调整方法**：
   - 激光点外偏 → 增大K_yaw
   - 激光点内偏 → 减小K_yaw
   - 目标：转弯时激光点保持稳定

### 3. 综合测试
1. **完整轨迹**：包含直线、转弯、加减速的复杂路径
2. **性能对比**：开启/关闭前馈控制的效果对比
3. **稳定性验证**：长时间运行稳定性测试

## 常见问题

### Q1: 前馈补偿后激光点更抖？
**可能原因**：
- IMU噪声过大
- 补偿系数过大
- 滤波不足

**解决方案**：
- 检查IMU安装是否牢固
- 减小补偿系数
- 增大滤波系数 (减小filter_alpha)

### Q2: 直线稳定但转弯仍抖动？
**可能原因**：
- 转弯检测阈值不合适
- 偏航补偿系数需要调整

**解决方案**：
- 调整turn_threshold
- 重新校准K_yaw参数

### Q3: 系统响应有延迟？
**可能原因**：
- 控制频率不够
- 滤波延迟过大

**解决方案**：
- 确保任务以5ms周期运行
- 减小滤波延迟

## 性能指标

### 预期改善效果
- **动态响应时间**：从100-200ms降低到10-20ms
- **激光点抖动**：减少60-80%
- **跟踪精度**：提高30-50%

### 系统资源消耗
- **CPU占用**：约2-3% (5ms任务周期)
- **内存占用**：约200字节 (前馈控制器结构体)
- **通信带宽**：无额外占用

## 技术支持

如遇到问题，请提供以下信息：
1. 串口调试输出
2. 具体测试场景描述
3. 当前参数配置
4. 问题现象视频 (如可能)

---
*本文档基于米醋电子工作室前馈控制理论，结合实际工程实现编写*
